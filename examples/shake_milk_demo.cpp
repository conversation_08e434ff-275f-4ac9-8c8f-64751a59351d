/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include <iostream>
#include <thread>
#include <chrono>

#include "right_robot.h"
#include "coffee_types.h"

using namespace aubo;

/**
 * @brief 演示右臂摇奶功能的示例程序
 * 
 * 这个示例展示了如何使用右臂机器人执行完整的取奶和摇奶流程：
 * 1. 初始化右臂机器人
 * 2. 移动到初始位置
 * 3. 执行取奶序列
 * 4. 执行摇奶动作（新功能）
 * 5. 准备拉花位置
 */
int main() {
    std::cout << "=== 右臂摇奶功能演示 ===" << std::endl;

    // 创建右臂机器人实例
    RightRobot right_robot;

    // 初始化机器人
    std::cout << "正在初始化右臂机器人..." << std::endl;
    if (!right_robot.init()) {
        std::cerr << "错误: 右臂机器人初始化失败" << std::endl;
        return -1;
    }
    std::cout << "右臂机器人初始化成功" << std::endl;

    // 移动到初始位置
    std::cout << "\n正在移动到初始位置..." << std::endl;
    if (!right_robot.move_to_home()) {
        std::cerr << "错误: 移动到初始位置失败" << std::endl;
        return -1;
    }
    std::cout << "已移动到初始位置" << std::endl;

    // 等待用户确认
    std::cout << "\n按回车键开始执行取奶序列..." << std::endl;
    std::cin.get();

    // 执行取奶序列
    std::cout << "正在执行取奶序列..." << std::endl;
    if (!right_robot.get_milk()) {
        std::cerr << "错误: 取奶序列执行失败" << std::endl;
        return -1;
    }
    std::cout << "取奶序列执行成功" << std::endl;

    // 等待用户确认
    std::cout << "\n按回车键开始执行摇奶动作..." << std::endl;
    std::cin.get();

    // 执行摇奶动作（新功能）
    std::cout << "正在执行摇奶动作..." << std::endl;
    if (!right_robot.shake_milk()) {
        std::cerr << "错误: 摇奶动作执行失败" << std::endl;
        return -1;
    }
    std::cout << "摇奶动作执行成功" << std::endl;

    // 等待用户确认
    std::cout << "\n按回车键准备拉花位置..." << std::endl;
    std::cin.get();

    // 准备拉花位置
    std::cout << "正在准备拉花位置..." << std::endl;
    if (!right_robot.prepare_for_latte_art()) {
        std::cerr << "错误: 准备拉花位置失败" << std::endl;
        return -1;
    }
    std::cout << "拉花位置准备完成" << std::endl;

    // 演示完成
    std::cout << "\n=== 摇奶功能演示完成 ===" << std::endl;
    std::cout << "右臂已完成取奶、摇奶和拉花准备的完整流程" << std::endl;
    std::cout << "现在可以与左臂配合进行拉花操作" << std::endl;

    return 0;
}
