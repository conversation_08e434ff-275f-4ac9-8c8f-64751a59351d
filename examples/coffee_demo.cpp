/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include <vector>

#include <aubo-base/log.h>
#include "../src/coffee.h"

using namespace aubo;

int main() {
    LOG_INFO("[coffee-demo] 开始咖啡制作演示");

    Coffee coffee_system;

    if (!coffee_system.init()) {
        LOG_ERROR("[coffee-demo] 系统初始化失败");
        return 1;
    }

    coffee_system.move_all_to_home();

    // 创建咖啡订单
    std::vector<CoffeeOrder> orders = {
        CoffeeOrder("ORDER_001", CoffeeType::AMERICANO, LatteArtType::NONE, 1),
        CoffeeOrder("ORDER_002", CoffeeType::LATTE, LatteArtType::HEART, 1),
        CoffeeOrder("ORDER_003", CoffeeType::CAPPUCCINO, LatteArtType::LEAF, 2),
        CoffeeOrder("ORDER_004", CoffeeType::CAPPUCCINO, LatteArtType::SWAN, 1)
    };

    // 制作咖啡
    for (const auto& order : orders) {
        LOG_INFO("[coffee-demo] 制作订单: {} - {} ({})",
                 order.order_id.c_str(),
                 get_coffee_type_name(order.type).c_str(),
                 get_latte_art_name(order.latte_art).c_str());

        coffee_system.make_coffee(order);
    }

    coffee_system.shutdown();
    LOG_INFO("[coffee-demo] 演示完成");

    return 0;
}
