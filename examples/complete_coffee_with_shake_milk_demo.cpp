/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include <iostream>
#include <thread>
#include <chrono>

#include "left_robot.h"
#include "right_robot.h"
#include "coffee_types.h"

using namespace aubo;

/**
 * @brief 完整的咖啡制作流程演示，包含新的摇奶功能
 * 
 * 这个示例展示了左右臂机器人协作制作拿铁咖啡的完整流程：
 * 左臂：取杯 -> 取咖啡 -> 准备拉花 -> 执行拉花
 * 右臂：取奶 -> 摇奶（新功能）-> 准备拉花 -> 协助拉花
 */
int main() {
    std::cout << "=== 完整咖啡制作流程演示（含摇奶功能）===" << std::endl;

    // 创建左右臂机器人实例
    LeftRobot left_robot;
    RightRobot right_robot;

    // 初始化机器人
    std::cout << "正在初始化双臂机器人..." << std::endl;
    
    if (!left_robot.init()) {
        std::cerr << "错误: 左臂机器人初始化失败" << std::endl;
        return -1;
    }
    std::cout << "左臂机器人初始化成功" << std::endl;

    if (!right_robot.init()) {
        std::cerr << "错误: 右臂机器人初始化失败" << std::endl;
        return -1;
    }
    std::cout << "右臂机器人初始化成功" << std::endl;

    // 移动到初始位置
    std::cout << "\n正在移动双臂到初始位置..." << std::endl;
    if (!left_robot.move_to_home() || !right_robot.move_to_home()) {
        std::cerr << "错误: 移动到初始位置失败" << std::endl;
        return -1;
    }
    std::cout << "双臂已移动到初始位置" << std::endl;

    // 等待用户确认开始制作
    std::cout << "\n按回车键开始制作拿铁咖啡..." << std::endl;
    std::cin.get();

    // 阶段1：左臂取杯，右臂取奶（并行执行）
    std::cout << "\n=== 阶段1：取杯和取奶 ===" << std::endl;
    
    // 使用线程并行执行左臂取杯和右臂取奶
    std::thread left_thread([&left_robot]() {
        std::cout << "左臂：开始取杯..." << std::endl;
        if (left_robot.get_cup()) {
            std::cout << "左臂：取杯成功" << std::endl;
        } else {
            std::cout << "左臂：取杯失败" << std::endl;
        }
    });

    std::thread right_thread([&right_robot]() {
        std::cout << "右臂：开始取奶..." << std::endl;
        if (right_robot.get_milk()) {
            std::cout << "右臂：取奶成功" << std::endl;
        } else {
            std::cout << "右臂：取奶失败" << std::endl;
        }
    });

    // 等待两个线程完成
    left_thread.join();
    right_thread.join();

    // 阶段2：左臂取咖啡，右臂摇奶
    std::cout << "\n=== 阶段2：取咖啡和摇奶 ===" << std::endl;
    
    std::cout << "左臂：开始取咖啡..." << std::endl;
    if (!left_robot.get_coffee()) {
        std::cerr << "错误: 左臂取咖啡失败" << std::endl;
        return -1;
    }
    std::cout << "左臂：取咖啡成功" << std::endl;

    std::cout << "右臂：开始摇奶..." << std::endl;
    if (!right_robot.shake_milk()) {
        std::cerr << "错误: 右臂摇奶失败" << std::endl;
        return -1;
    }
    std::cout << "右臂：摇奶成功" << std::endl;

    // 阶段3：双臂准备拉花
    std::cout << "\n=== 阶段3：准备拉花 ===" << std::endl;
    
    std::thread left_prepare([&left_robot]() {
        std::cout << "左臂：准备拉花位置..." << std::endl;
        if (left_robot.prepare_for_latte_art()) {
            std::cout << "左臂：拉花位置准备完成" << std::endl;
        } else {
            std::cout << "左臂：拉花位置准备失败" << std::endl;
        }
    });

    std::thread right_prepare([&right_robot]() {
        std::cout << "右臂：准备拉花位置..." << std::endl;
        if (right_robot.prepare_for_latte_art()) {
            std::cout << "右臂：拉花位置准备完成" << std::endl;
        } else {
            std::cout << "右臂：拉花位置准备失败" << std::endl;
        }
    });

    left_prepare.join();
    right_prepare.join();

    // 阶段4：执行拉花
    std::cout << "\n=== 阶段4：执行拉花 ===" << std::endl;
    
    LatteArtType art_type = LatteArtType::HEART; // 选择心形拉花
    std::cout << "开始执行" << get_latte_art_name(art_type) << "拉花..." << std::endl;

    // 双臂协作执行拉花
    std::thread left_art([&left_robot, art_type]() {
        if (left_robot.do_latte_art(art_type)) {
            std::cout << "左臂：拉花执行成功" << std::endl;
        } else {
            std::cout << "左臂：拉花执行失败" << std::endl;
        }
    });

    std::thread right_art([&right_robot, art_type]() {
        if (right_robot.do_latte_art(art_type)) {
            std::cout << "右臂：拉花协助成功" << std::endl;
        } else {
            std::cout << "右臂：拉花协助失败" << std::endl;
        }
    });

    left_art.join();
    right_art.join();

    // 制作完成
    std::cout << "\n=== 咖啡制作完成 ===" << std::endl;
    std::cout << "🎉 拿铁咖啡制作成功！" << std::endl;
    std::cout << "✅ 包含完整的取杯、取咖啡、取奶、摇奶和拉花流程" << std::endl;
    std::cout << "☕ 享用您的" << get_latte_art_name(art_type) << "拿铁咖啡吧！" << std::endl;

    return 0;
}
