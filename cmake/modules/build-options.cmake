# Copyright ©2015-2024 Aubo (Beijing) Robotics Technology Co., Ltd. All rights reserved.

# Build type options
option(BUILD_EXECUTABLE "Build executable" ON)
option(BUILD_EXECUTABLE_IS_SERVICE "Build executable as a service" ON)
option(BUILD_SHARED_LIBS "Build shared libraries" OFF)
option(BUILD_TESTING "Build tests" OFF)
option(BUILD_EXAMPLES "Build examples" ON)
option(BUILD_DOCS "Build documentation" OFF)

if(BUILD_EXECUTABLE)
    if (BUILD_SHARED_LIBS)
        message(FATAL_ERROR "BUILD_EXECUTABLE and BUILD_SHARED_LIBS cannot both be ON")
    endif()
else()
    if (BUILD_EXECUTABLE_IS_SERVICE)
        message(FATAL_ERROR "BUILD_EXECUTABLE_IS_SERVICE cannot be ON when BUILD_EXECUTABLE is OFF")
    endif()
endif()

# Enable testing if requested
if(BUILD_TESTING)
    include(CTest)
    enable_testing()
endif()

# Print build configuration
message(STATUS "")
message(STATUS "Build configuration:")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  Executable: ${BUILD_EXECUTABLE}")
message(STATUS "  Executable as service: ${BUILD_EXECUTABLE_IS_SERVICE}")
message(STATUS "  Shared libs: ${BUILD_SHARED_LIBS}")
message(STATUS "  Tests: ${BUILD_TESTING}")
message(STATUS "  Examples: ${BUILD_EXAMPLES}")
message(STATUS "  Documentation: ${BUILD_DOCS}")

# Add feature information
include(FeatureSummary)
add_feature_info(BUILD_EXECUTABLE BUILD_EXECUTABLE "Build executable")
add_feature_info(BUILD_EXECUTABLE_IS_SERVICE BUILD_EXECUTABLE_IS_SERVICE "Build executable as a service")
add_feature_info(BUILD_SHARED_LIBS BUILD_SHARED_LIBS "Build shared libraries")
add_feature_info(BUILD_TESTING BUILD_TESTING "Build tests")
add_feature_info(BUILD_EXAMPLES BUILD_EXAMPLES "Build examples")
add_feature_info(BUILD_DOCS BUILD_DOCS "Build documentation")