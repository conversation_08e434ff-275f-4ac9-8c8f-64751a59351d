# Copyright ©2015-2024 Aubo (Beijing) Robotics Technology Co., Ltd. All rights reserved.

# Compiler options
option(AUBO_ENABLE_EXTRA_WARNINGS "Enable extra compiler warnings" OFF)
option(AUBO_WARNINGS_AS_ERRORS "Treat compiler warnings as errors" OFF)

# Compiler flags and optimizations
if(CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")

    # Basic warning flags (always enabled)
    add_compile_options(-Wall -Wextra)

    # Extra warning flags (optional but recommended)
    if(AUBO_ENABLE_EXTRA_WARNINGS)
        add_compile_options(
            -Wpedantic
            -Wshadow
            -Wcast-align
            -Wunused
            -Woverloaded-virtual
            -Wconversion
            -Wsign-conversion
            -Wnull-dereference
            -Wdouble-promotion
            -Wformat=2
            -Wmissing-declarations
            -Wredundant-decls
        )
    endif()

    # Treat warnings as errors
    if(AUBO_WARNINGS_AS_ERRORS)
        add_compile_options(-Werror)
    endif()

    # Build type specific flags
    if(CMAKE_BUILD_TYPE STREQUAL "Debug")
        add_compile_options(-fno-omit-frame-pointer -g)
        if(CMAKE_CXX_COMPILER_ID MATCHES "GNU")
            add_compile_options(-fdiagnostics-color=always)
        endif()
    elseif(CMAKE_BUILD_TYPE STREQUAL "Release")
        add_compile_options(-O3 -DNDEBUG)
    elseif(CMAKE_BUILD_TYPE STREQUAL "RelWithDebInfo")
        add_compile_options(-O2 -g -DNDEBUG)
    elseif(CMAKE_BUILD_TYPE STREQUAL "MinSizeRel")
        add_compile_options(-Os -DNDEBUG)
    endif()
endif()

if(MSVC)
    # Basic warning level (always enabled)
    add_compile_options(/W3)

    # Extra warning flags (optional)
    if(AUBO_ENABLE_EXTRA_WARNINGS)
        add_compile_options(
            /W4
            /permissive-
        )
    endif()

    # Treat warnings as errors (optional)
    if(AUBO_WARNINGS_AS_ERRORS)
        add_compile_options(/WX)
    endif()

    # Character set and language options
    add_compile_options(
        /utf-8
        /Zc:__cplusplus
        /volatile:iso
    )

    # Disable specific warnings
    add_compile_options(
        /wd4251  # class needs to have dll-interface
        /wd4275  # non dll-interface class used as base
    )

    # Windows-specific definitions
    add_definitions(
        -DNOMINMAX
        -D_USE_MATH_DEFINES
        -D_CRT_SECURE_NO_WARNINGS
        -D_WIN32_WINNT=0x0601
    )

    # Export symbols
    set(CMAKE_WINDOWS_EXPORT_ALL_SYMBOLS ON)
endif()

# only debug mode support asan
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -fsanitize=address -fno-omit-frame-pointer")
    set(CMAKE_EXE_LINKER_FLAGS_DEBUG "${CMAKE_EXE_LINKER_FLAGS_DEBUG} -fsanitize=address -fno-omit-frame-pointer")
endif()

# Print compiler information
message(STATUS "")
message(STATUS "Compiler configuration:")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  Compiler: ${CMAKE_CXX_COMPILER_ID} ${CMAKE_CXX_COMPILER_VERSION}")
message(STATUS "  Extra warnings: ${AUBO_ENABLE_EXTRA_WARNINGS}")
message(STATUS "  Warnings as errors: ${AUBO_WARNINGS_AS_ERRORS}")
if(MSVC)
    message(STATUS "  Windows SDK: ${CMAKE_VS_WINDOWS_TARGET_PLATFORM_VERSION}")
endif()
message(STATUS "")