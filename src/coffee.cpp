/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include "coffee.h"
#include "left_robot.h"
#include "right_robot.h"

#include <aubo-base/log.h>
#include <thread>
#include <chrono>
#include <taskflow/taskflow.hpp>

namespace aubo {

class Coffee::Impl {
public:
    Impl() : status_(CoffeeStatus::IDLE) {
        LOG_INFO("[Coffee] Impl 构造函数");
    }

    ~Impl() {
        LOG_INFO("[Coffee] Impl 析构函数");
        shutdown();
    }

    bool init() {
        LOG_INFO("[Coffee] 初始化咖啡制作系统");

        status_ = CoffeeStatus::INITIALIZING;

        // 创建左右臂机器人实例
        left_robot_ = std::make_unique<LeftRobot>();
        right_robot_ = std::make_unique<RightRobot>();

        // 使用Taskflow并行初始化左右臂
        tf::Executor executor;
        tf::Taskflow taskflow;

        // 用于存储初始化结果
        bool left_init_success = false;
        bool right_init_success = false;

        // 创建左臂初始化任务
        auto left_init_task = taskflow.emplace([this, &left_init_success]() {
            LOG_INFO("[Coffee] 开始初始化左臂");
            left_init_success = left_robot_->init();
            if (left_init_success) {
                LOG_INFO("[Coffee] 左臂初始化成功");
            } else {
                LOG_ERROR("[Coffee] 左臂初始化失败");
            }
        });

        // 创建右臂初始化任务
        auto right_init_task = taskflow.emplace([this, &right_init_success]() {
            LOG_INFO("[Coffee] 开始初始化右臂");
            right_init_success = right_robot_->init();
            if (right_init_success) {
                LOG_INFO("[Coffee] 右臂初始化成功");
            } else {
                LOG_ERROR("[Coffee] 右臂初始化失败");
            }
        });

        // 设置任务名称（用于调试）
        left_init_task.name("左臂初始化");
        right_init_task.name("右臂初始化");

        // 执行并行初始化
        LOG_INFO("[Coffee] 开始并行初始化左右臂");
        executor.run(taskflow).wait();

        // 检查初始化结果
        if (!left_init_success || !right_init_success) {
            LOG_ERROR("[Coffee] 机器人臂初始化失败 - 左臂: {}, 右臂: {}",
                     left_init_success ? "成功" : "失败",
                     right_init_success ? "成功" : "失败");
            status_ = CoffeeStatus::ERROR;
            return false;
        }

        status_ = CoffeeStatus::IDLE;
        LOG_INFO("[Coffee] 咖啡制作系统初始化完成 - 左右臂并行初始化成功");
        return true;
    }

    bool shutdown() {
        LOG_INFO("[Coffee] 关闭咖啡制作系统");

        if (status_ == CoffeeStatus::MAKING) {
            LOG_WARN("[Coffee] 系统正在制作咖啡，执行紧急停止");
            emergency_stop();
        }

        // 使用Taskflow并行关闭左右臂
        if (left_robot_ && right_robot_) {
            tf::Executor executor;
            tf::Taskflow taskflow;

            // 创建左臂关闭任务
            auto left_shutdown_task = taskflow.emplace([this]() {
                LOG_INFO("[Coffee] 开始关闭左臂");
                if (left_robot_->move_to_home()) {
                    LOG_INFO("[Coffee] 左臂安全关闭完成");
                } else {
                    LOG_WARN("[Coffee] 左臂关闭时移动到初始位置失败");
                }
            });

            // 创建右臂关闭任务
            auto right_shutdown_task = taskflow.emplace([this]() {
                LOG_INFO("[Coffee] 开始关闭右臂");
                if (right_robot_->move_to_home()) {
                    LOG_INFO("[Coffee] 右臂安全关闭完成");
                } else {
                    LOG_WARN("[Coffee] 右臂关闭时移动到初始位置失败");
                }
            });

            // 设置任务名称
            left_shutdown_task.name("左臂关闭");
            right_shutdown_task.name("右臂关闭");

            // 执行并行关闭
            LOG_INFO("[Coffee] 开始并行关闭左右臂");
            executor.run(taskflow).wait();
        } else {
            // 单独处理存在的机器人臂
            if (left_robot_) {
                left_robot_->move_to_home();
            }
            if (right_robot_) {
                right_robot_->move_to_home();
            }
        }

        // 重置机器人实例
        left_robot_.reset();
        right_robot_.reset();

        status_ = CoffeeStatus::IDLE;
        LOG_INFO("[Coffee] 咖啡制作系统已关闭");
        return true;
    }

    bool move_all_to_home() {
        LOG_INFO("[Coffee] 将所有机器人臂移动到初始位置");

        if (!left_robot_ || !right_robot_) {
            LOG_ERROR("[Coffee] 机器人臂未初始化");
            return false;
        }

        // 使用Taskflow并行移动左右臂
        tf::Executor executor;
        tf::Taskflow taskflow;

        // 用于存储移动结果
        bool left_move_success = false;
        bool right_move_success = false;

        // 创建左臂移动任务
        auto left_move_task = taskflow.emplace([this, &left_move_success]() {
            LOG_INFO("[Coffee] 开始移动左臂到初始位置");
            left_move_success = left_robot_->move_to_home();
            if (left_move_success) {
                LOG_INFO("[Coffee] 左臂移动到初始位置成功");
            } else {
                LOG_ERROR("[Coffee] 左臂移动到初始位置失败");
            }
        });

        // 创建右臂移动任务
        auto right_move_task = taskflow.emplace([this, &right_move_success]() {
            LOG_INFO("[Coffee] 开始移动右臂到初始位置");
            right_move_success = right_robot_->move_to_home();
            if (right_move_success) {
                LOG_INFO("[Coffee] 右臂移动到初始位置成功");
            } else {
                LOG_ERROR("[Coffee] 右臂移动到初始位置失败");
            }
        });

        // 设置任务名称（用于调试）
        left_move_task.name("左臂移动到初始位置");
        right_move_task.name("右臂移动到初始位置");

        // 执行并行移动
        LOG_INFO("[Coffee] 开始并行移动左右臂到初始位置");
        executor.run(taskflow).wait();

        // 检查移动结果
        bool success = left_move_success && right_move_success;
        if (success) {
            LOG_INFO("[Coffee] 所有机器人臂已并行移动到初始位置");
        } else {
            LOG_ERROR("[Coffee] 机器人臂移动失败 - 左臂: {}, 右臂: {}",
                     left_move_success ? "成功" : "失败",
                     right_move_success ? "成功" : "失败");
        }

        return success;
    }

    bool move_to_home(RobotArm arm) {
        LOG_INFO("[Coffee] 将{}移动到初始位置", get_arm_name(arm));

        switch (arm) {
            case RobotArm::LEFT:
                if (left_robot_) {
                    return left_robot_->move_to_home();
                }
                LOG_ERROR("[Coffee] 左臂未初始化");
                return false;

            case RobotArm::RIGHT:
                if (right_robot_) {
                    return right_robot_->move_to_home();
                }
                LOG_ERROR("[Coffee] 右臂未初始化");
                return false;

            case RobotArm::BOTH:
                return move_all_to_home();

            default:
                LOG_ERROR("[Coffee] 未知的机器人臂类型");
                return false;
        }
    }

    bool make_coffee(const CoffeeOrder& order) {
        LOG_INFO("[Coffee] 开始制作咖啡: {} (订单ID: {}, 数量: {}, 拉花: {})",
                 get_coffee_type_name(order.type),
                 order.order_id,
                 order.quantity,
                 get_latte_art_name(order.latte_art));

        if (status_ != CoffeeStatus::IDLE) {
            LOG_ERROR("[Coffee] 系统状态不正确，当前状态: {}", get_status_string());
            return false;
        }

        if (!left_robot_) {
            LOG_ERROR("[Coffee] 左臂未初始化");
            return false;
        }

        if (!right_robot_) {
            LOG_ERROR("[Coffee] 右臂未初始化");
            return false;
        }

        status_ = CoffeeStatus::PREPARING;

        // 制作指定数量的咖啡
        for (int i = 0; i < order.quantity; i++) {
            LOG_INFO("[Coffee] 制作第 {}/{} 杯咖啡", i + 1, order.quantity);

            if (!make_single_coffee(order.type, order.latte_art)) {
                LOG_ERROR("[Coffee] 制作第 {} 杯咖啡失败", i + 1);
                status_ = CoffeeStatus::ERROR;
                return false;
            }

            LOG_INFO("[Coffee] 第 {} 杯咖啡制作完成", i + 1);
        }

        status_ = CoffeeStatus::COMPLETED;
        LOG_INFO("[Coffee] 咖啡订单制作完成");

        // 短暂延迟后回到空闲状态
        std::this_thread::sleep_for(std::chrono::seconds(2));
        status_ = CoffeeStatus::IDLE;

        return true;
    }

    CoffeeStatus get_status() const {
        return status_;
    }

    std::string get_status_string() const {
        switch (status_) {
            case CoffeeStatus::IDLE:        return "空闲";
            case CoffeeStatus::INITIALIZING: return "初始化中";
            case CoffeeStatus::PREPARING:   return "准备中";
            case CoffeeStatus::MAKING:      return "制作中";
            case CoffeeStatus::FINISHING:   return "完成中";
            case CoffeeStatus::COMPLETED:   return "已完成";
            case CoffeeStatus::ERROR:       return "错误";
            default:                        return "未知状态";
        }
    }

    bool is_arm_available(RobotArm arm) const {
        switch (arm) {
            case RobotArm::LEFT:
                return left_robot_ != nullptr && status_ == CoffeeStatus::IDLE;
            case RobotArm::RIGHT:
                return right_robot_ != nullptr && status_ == CoffeeStatus::IDLE;
            case RobotArm::BOTH:
                return is_arm_available(RobotArm::LEFT) && is_arm_available(RobotArm::RIGHT);
            default:
                return false;
        }
    }

    bool emergency_stop() {
        LOG_WARN("[Coffee] 执行紧急停止");

        // TODO: 实现紧急停止逻辑
        // 这里应该立即停止所有机器人的运动
        // 并将它们移动到安全位置

        status_ = CoffeeStatus::ERROR;

        // 尝试将所有机器人臂移动到安全位置
        move_all_to_home();

        LOG_WARN("[Coffee] 紧急停止完成");
        return true;
    }

private:
    bool make_single_coffee(CoffeeType type, LatteArtType latte_art_type) {
        status_ = CoffeeStatus::MAKING;

        // 1. 取杯子
        LOG_INFO("[Coffee] 步骤1: 取杯子");
        if (!left_robot_->get_cup()) {
            LOG_ERROR("[Coffee] 取杯子失败");
            return false;
        }

        // 2. 左臂取咖啡和右臂接奶并行执行
        LOG_INFO("[Coffee] 步骤2: 左臂取咖啡和右臂接奶并行执行");

        tf::Executor executor;
        tf::Taskflow coffee_milk_taskflow;

        bool left_get_coffee_success = false;
        bool right_get_milk_success = false;

        // 创建左臂取咖啡任务
        auto left_get_coffee_task = coffee_milk_taskflow.emplace([this, type, &left_get_coffee_success]() {
            LOG_INFO("[Coffee] 左臂开始取咖啡 {}", get_coffee_type_name(type));
            left_get_coffee_success = left_robot_->get_coffee();
            if (left_get_coffee_success) {
                LOG_INFO("[Coffee] 左臂取咖啡成功");
            } else {
                LOG_ERROR("[Coffee] 左臂取咖啡失败");
            }
        });

        // 创建右臂接奶任务（如果需要牛奶）
        auto right_get_milk_task = coffee_milk_taskflow.emplace([this, type, &right_get_milk_success]() {
            if (needs_milk(type)) {
                LOG_INFO("[Coffee] 右臂开始接奶");
                right_get_milk_success = right_robot_->get_milk();
                if (right_get_milk_success) {
                    LOG_INFO("[Coffee] 右臂接奶成功");
                } else {
                    LOG_ERROR("[Coffee] 右臂接奶失败");
                }
            } else {
                LOG_INFO("[Coffee] 无需牛奶，右臂跳过接奶步骤");
                right_get_milk_success = true; // 不需要牛奶时设为成功
            }
        });

        // 设置任务名称
        left_get_coffee_task.name("左臂取咖啡");
        right_get_milk_task.name("右臂接奶");

        // 执行并行任务
        executor.run(coffee_milk_taskflow).wait();

        // 检查执行结果
        if (!left_get_coffee_success) {
            LOG_ERROR("[Coffee] 左臂取咖啡失败");
            return false;
        }

        if (needs_milk(type) && !right_get_milk_success) {
            LOG_ERROR("[Coffee] 右臂接奶失败");
            return false;
        }

        LOG_INFO("[Coffee] 左臂取咖啡和右臂接奶并行执行完成");

        // 3. 右臂摇奶（如果需要牛奶的咖啡类型）
        if (needs_milk(type)) {
            LOG_INFO("[Coffee] 步骤3: 右臂摇奶");
            if (!right_robot_->shake_milk()) {
                LOG_ERROR("[Coffee] 右臂摇奶失败");
                return false;
            }
            LOG_INFO("[Coffee] 右臂摇奶完成");
        }

        // 4. 拉花（如果需要）
        if (latte_art_type != LatteArtType::NONE) {
            LOG_INFO("[Coffee] 步骤4: 制作{}拉花", get_latte_art_name(latte_art_type));

            // 4.1 左右臂并行准备拉花位置
            LOG_INFO("[Coffee] 步骤4.1: 左右臂并行准备拉花位置");

            tf::Executor executor;
            tf::Taskflow prepare_taskflow;

            bool left_prepare_success = false;
            bool right_prepare_success = false;

            // 创建左臂准备任务
            auto left_prepare_task = prepare_taskflow.emplace([this, &left_prepare_success]() {
                LOG_INFO("[Coffee] 左臂开始准备拉花位置");
                left_prepare_success = left_robot_->prepare_for_latte_art();
                if (left_prepare_success) {
                    LOG_INFO("[Coffee] 左臂拉花准备成功");
                } else {
                    LOG_ERROR("[Coffee] 左臂拉花准备失败");
                }
            });

            // 创建右臂准备任务
            auto right_prepare_task = prepare_taskflow.emplace([this, &right_prepare_success]() {
                LOG_INFO("[Coffee] 右臂开始准备拉花位置");
                right_prepare_success = right_robot_->prepare_for_latte_art();
                if (right_prepare_success) {
                    LOG_INFO("[Coffee] 右臂拉花准备成功");
                } else {
                    LOG_ERROR("[Coffee] 右臂拉花准备失败");
                }
            });

            // 设置任务名称
            left_prepare_task.name("左臂拉花准备");
            right_prepare_task.name("右臂拉花准备");

            // 执行并行准备
            executor.run(prepare_taskflow).wait();

            // 检查准备结果
            if (!left_prepare_success || !right_prepare_success) {
                LOG_ERROR("[Coffee] 拉花准备失败 - 左臂: {}, 右臂: {}",
                         left_prepare_success ? "成功" : "失败",
                         right_prepare_success ? "成功" : "失败");
                return false;
            }

            LOG_INFO("[Coffee] 左右臂拉花准备完成");

            // 4.2 左右臂并行执行拉花动作
            LOG_INFO("[Coffee] 步骤4.2: 左右臂配合执行拉花动作");

            tf::Taskflow latte_art_taskflow;

            bool left_latte_art_success = false;
            bool right_latte_art_success = false;

            // 创建左臂拉花任务
            auto left_latte_art_task = latte_art_taskflow.emplace([this, latte_art_type, &left_latte_art_success]() {
                LOG_INFO("[Coffee] 左臂开始执行拉花动作");
                left_latte_art_success = left_robot_->do_latte_art(latte_art_type);
                if (left_latte_art_success) {
                    LOG_INFO("[Coffee] 左臂拉花动作执行成功");
                } else {
                    LOG_ERROR("[Coffee] 左臂拉花动作执行失败");
                }
            });

            // 创建右臂拉花任务
            auto right_latte_art_task = latte_art_taskflow.emplace([this, latte_art_type, &right_latte_art_success]() {
                LOG_INFO("[Coffee] 右臂开始执行拉花动作");
                right_latte_art_success = right_robot_->do_latte_art(latte_art_type);
                if (right_latte_art_success) {
                    LOG_INFO("[Coffee] 右臂拉花动作执行成功");
                } else {
                    LOG_ERROR("[Coffee] 右臂拉花动作执行失败");
                }
            });

            // 设置任务名称
            left_latte_art_task.name("左臂拉花执行");
            right_latte_art_task.name("右臂拉花执行");

            // 执行并行拉花
            executor.run(latte_art_taskflow).wait();

            // 检查拉花结果
            if (!left_latte_art_success || !right_latte_art_success) {
                LOG_ERROR("[Coffee] 拉花动作执行失败 - 左臂: {}, 右臂: {}",
                         left_latte_art_success ? "成功" : "失败",
                         right_latte_art_success ? "成功" : "失败");
                return false;
            }

            LOG_INFO("[Coffee] 左右臂拉花配合完成");
        } else {
            LOG_INFO("[Coffee] 步骤4: 无需拉花");
        }

        status_ = CoffeeStatus::FINISHING;

        // 5. 完成制作，交付咖啡
        LOG_INFO("[Coffee] 步骤5: 左臂交付咖啡");
        if (!left_robot_->deliver_coffee()) {
            LOG_ERROR("[Coffee] 左臂交付咖啡失败");
            return false;
        }
        LOG_INFO("[Coffee] 左臂咖啡交付完成");

        // 6. 右臂后续处理（仅在需要牛奶的咖啡类型中执行）
        if (latte_art_type != LatteArtType::NONE) {
            // 6.1 右臂倾倒剩余牛奶
            LOG_INFO("[Coffee] 步骤6.1: 右臂倾倒剩余牛奶");
            if (!right_robot_->pour_remaining_milk()) {
                LOG_ERROR("[Coffee] 右臂倾倒剩余牛奶失败");
                return false;
            }
            LOG_INFO("[Coffee] 右臂倾倒剩余牛奶完成");

            // 6.2 右臂清洗
            LOG_INFO("[Coffee] 步骤6.2: 右臂清洗");
            if (!right_robot_->clean()) {
                LOG_ERROR("[Coffee] 右臂清洗失败");
                return false;
            }
            LOG_INFO("[Coffee] 右臂清洗完成");
        }

        LOG_INFO("[Coffee] 咖啡制作流程全部完成");
        return true;
    }

    std::string get_arm_name(RobotArm arm) const {
        switch (arm) {
            case RobotArm::LEFT:  return "左臂";
            case RobotArm::RIGHT: return "右臂";
            case RobotArm::BOTH:  return "双臂";
            default:              return "未知臂";
        }
    }

    bool needs_milk(CoffeeType type) const {
        // 判断咖啡类型是否需要牛奶
        switch (type) {
            case CoffeeType::LATTE:
            case CoffeeType::CAPPUCCINO:
                return true;
            case CoffeeType::AMERICANO:
            default:
                return false;
        }
    }

    std::unique_ptr<LeftRobot> left_robot_;
    std::unique_ptr<RightRobot> right_robot_;

    CoffeeStatus status_;
};

Coffee::Coffee() {
    impl_ = std::make_unique<Impl>();
}

Coffee::~Coffee() = default;

bool Coffee::init() {
    return impl_->init();
}

bool Coffee::shutdown() {
    return impl_->shutdown();
}

bool Coffee::move_all_to_home() {
    return impl_->move_all_to_home();
}

bool Coffee::move_to_home(RobotArm arm) {
    return impl_->move_to_home(arm);
}

bool Coffee::make_coffee(const CoffeeOrder& order) {
    return impl_->make_coffee(order);
}

CoffeeStatus Coffee::get_status() const {
    return impl_->get_status();
}

std::string Coffee::get_status_string() const {
    return impl_->get_status_string();
}

bool Coffee::is_arm_available(RobotArm arm) const {
    return impl_->is_arm_available(arm);
}

bool Coffee::emergency_stop() {
    return impl_->emergency_stop();
}

std::string Coffee::get_coffee_type_name(CoffeeType type) {
    return aubo::get_coffee_type_name(type);
}

std::string Coffee::get_latte_art_name(LatteArtType type) {
    return aubo::get_latte_art_name(type);
}

bool Coffee::is_coffee_suitable_for_latte_art(CoffeeType type) {
    return aubo::is_coffee_suitable_for_latte_art(type);
}

LatteArtType Coffee::get_recommended_latte_art(CoffeeType type) {
    return aubo::get_recommended_latte_art(type);
}

} // namespace aubo
