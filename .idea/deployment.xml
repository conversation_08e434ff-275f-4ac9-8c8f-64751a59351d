<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="PublishConfigData" remoteFilesAllowedToDisappearOnAutoupload="false">
    <serverData>
      <paths name="Dell-WG (65e1a109-3924-41a5-8eb7-7118ba1f4d9d)">
        <serverdata>
          <mappings>
            <mapping deploy="/tmp/tmp.xnKl09zJ0t/aubo-coffee-service" local="$PROJECT_DIR$" />
          </mappings>
          <excludedPaths>
            <excludedPath local="true" path="$PROJECT_DIR$/cmake-build-debug-dell-wg" />
          </excludedPaths>
        </serverdata>
      </paths>
      <paths name="Dev (f83a9102-7869-4345-8870-0ff174695938)">
        <serverdata>
          <mappings>
            <mapping deploy="/tmp/tmp.LRdaNnguM5/aubo-coffee-service" local="$PROJECT_DIR$" />
          </mappings>
          <excludedPaths>
            <excludedPath local="true" path="$PROJECT_DIR$/cmake-build-debug-dev" />
          </excludedPaths>
        </serverdata>
      </paths>
      <paths name="Dev-Coffee (8611a8ea-6b96-4a50-99ad-13f04e60dacf)">
        <serverdata>
          <mappings>
            <mapping deploy="/tmp/tmp.0vnjS9MXLc/aubo-coffee-service" local="$PROJECT_DIR$" />
          </mappings>
          <excludedPaths>
            <excludedPath local="true" path="$PROJECT_DIR$/cmake-build-debug-dev-coffee" />
          </excludedPaths>
        </serverdata>
      </paths>
      <paths name="Dev-PE (bb8810b3-31e4-492d-981c-3ae172be6023)">
        <serverdata>
          <mappings>
            <mapping deploy="/tmp/tmp.L6TuMft6P5/aubo-coffee-service" local="$PROJECT_DIR$" />
          </mappings>
          <excludedPaths>
            <excludedPath local="true" path="$PROJECT_DIR$/cmake-build-debug-dev-pe" />
          </excludedPaths>
        </serverdata>
      </paths>
    </serverData>
  </component>
</project>