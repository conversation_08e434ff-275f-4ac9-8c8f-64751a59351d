# 右臂摇奶功能说明

## 概述

为右臂机器人新增了摇奶功能，用于在取完奶后执行转圈摇奶动作，使牛奶充分混合，为后续的拉花操作做准备。

## 功能特点

### 1. 摇奶轨迹设计
- **3个关键位置**：摇奶起始位置、中间位置、结束位置
- **循环执行**：默认执行3个循环，确保充分摇奶
- **流畅运动**：使用较大的混合半径，确保运动轨迹平滑

### 2. 运动参数优化
- **线性加速度**：0.8 m/s²（相对较高，产生摇奶效果）
- **角度加速度**：3.0 rad/s²（相对较高，产生摇奶效果）
- **线性速度**：0.8 m/s（适中速度，确保安全）
- **角度速度**：3.0 rad/s（适中速度，确保安全）
- **混合半径**：0.02 m（较大半径，运动更流畅）

### 3. 关节位置数据
```cpp
// 摇奶序列的3个关键位置
{-0.054118, 0.259726, 2.186295, -1.240109, -1.207265, 0.114604}, // 摇奶起始位置
{-0.151755, 0.267723, 2.191979, -1.241637, -1.304873, 0.112070}, // 摇奶中间位置
{-0.218171, 0.161130, 2.110561, -1.216082, -1.371269, 0.110424}  // 摇奶结束位置
```

## API 接口

### 新增方法

```cpp
/**
 * @brief 执行摇奶动作
 *
 * 在取完奶后执行转圈摇奶动作，使牛奶充分混合，为拉花做准备
 * 通常在 get_milk() 之后、prepare_for_latte_art() 之前调用
 *
 * @return 如果摇奶动作执行成功则返回true
 */
bool shake_milk();
```

### 使用示例

```cpp
#include "right_robot.h"

using namespace aubo;

int main() {
    RightRobot right_robot;
    
    // 初始化
    if (!right_robot.init()) {
        return -1;
    }
    
    // 移动到初始位置
    right_robot.move_to_home();
    
    // 取奶
    right_robot.get_milk();
    
    // 摇奶（新功能）
    right_robot.shake_milk();
    
    // 准备拉花
    right_robot.prepare_for_latte_art();
    
    // 执行拉花
    right_robot.do_latte_art(LatteArtType::HEART);
    
    return 0;
}
```

## 完整工作流程

### 标准拿铁制作流程（含摇奶）

1. **初始化阶段**
   - 左臂和右臂初始化
   - 移动到初始位置

2. **准备阶段**（并行执行）
   - 左臂：执行取杯序列
   - 右臂：执行取奶序列

3. **制作阶段**
   - 左臂：执行取咖啡序列
   - 右臂：执行摇奶动作 ⭐（新功能）

4. **拉花准备阶段**（并行执行）
   - 左臂：准备拉花位置
   - 右臂：准备拉花位置

5. **拉花执行阶段**（协作执行）
   - 左臂和右臂：协作执行拉花动作

## 安全特性

### 1. 状态检查
- 在执行摇奶前检查机器人连接和初始化状态
- 确保机器人处于可操作状态

### 2. 位置检测
- 在 `move_to_home()` 中增加了摇奶序列位置检测
- 如果机器人在摇奶过程中被中断，能够安全返回初始位置

### 3. 错误处理
- 每个摇奶循环都有独立的错误检查
- 如果任何一个循环失败，整个摇奶过程会停止并返回错误

## 技术实现细节

### 1. 轨迹执行
- 使用与拉花相同的轨迹执行框架 `execute_trajectory_from_config()`
- 支持多次循环执行同一轨迹
- 使用 B 样条插值确保运动平滑

### 2. 运动控制
- 专门为摇奶优化的运动参数
- 较高的加速度和速度设置，产生有效的摇奶效果
- 适当的混合半径，确保轨迹连续性

### 3. 代码结构
- 遵循现有代码架构和命名规范
- 与其他功能模块保持一致的接口设计
- 完整的日志记录和错误处理

## 示例程序

项目中提供了两个示例程序：

1. **`examples/shake_milk_demo.cpp`**
   - 专门演示摇奶功能的简单示例
   - 逐步展示取奶、摇奶、准备拉花的流程

2. **`examples/complete_coffee_with_shake_milk_demo.cpp`**
   - 完整的双臂协作咖啡制作演示
   - 包含摇奶功能的完整拿铁制作流程

## 配置和调试

### 运动参数调整
如需调整摇奶效果，可以修改以下参数：
- `shake_cycles`：摇奶循环次数（默认3次）
- 运动参数：加速度、速度、混合半径
- 轨迹点：3个关键位置的关节角度

### 日志输出
摇奶功能包含详细的日志输出：
- 摇奶序列开始和结束
- 每个摇奶循环的执行状态
- 错误信息和调试信息

## 注意事项

1. **执行顺序**：摇奶功能应在取奶之后、准备拉花之前执行
2. **安全距离**：确保摇奶过程中机器人周围有足够的安全空间
3. **参数调整**：根据实际需要调整摇奶循环次数和运动参数
4. **错误处理**：如果摇奶失败，应检查机器人状态和轨迹数据
