// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: sensor.proto

#ifndef PROTOBUF_sensor_2eproto__INCLUDED
#define PROTOBUF_sensor_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 2006000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 2006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "robotcommon.pb.h"
// @@protoc_insertion_point(includes)

namespace peripheral {

// Internal implementation detail -- do not call these.
void  protobuf_AddDesc_sensor_2eproto();
void protobuf_AssignDesc_sensor_2eproto();
void protobuf_ShutdownFile_sensor_2eproto();

class ProtoWrench;
class ProtoResponseWrench;
class ProtoFtSensorCalibParam;
class ProtoFtSensorCalibResult;
class ProtoResponseFtSensorCalibResult;

// ===================================================================

class ProtoWrench : public ::google::protobuf::Message {
 public:
  ProtoWrench();
  virtual ~ProtoWrench();

  ProtoWrench(const ProtoWrench& from);

  inline ProtoWrench& operator=(const ProtoWrench& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoWrench& default_instance();

  void Swap(ProtoWrench* other);

  // implements Message ----------------------------------------------

  ProtoWrench* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoWrench& from);
  void MergeFrom(const ProtoWrench& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated double force = 1;
  inline int force_size() const;
  inline void clear_force();
  static const int kForceFieldNumber = 1;
  inline double force(int index) const;
  inline void set_force(int index, double value);
  inline void add_force(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      force() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_force();

  // repeated double torque = 2;
  inline int torque_size() const;
  inline void clear_torque();
  static const int kTorqueFieldNumber = 2;
  inline double torque(int index) const;
  inline void set_torque(int index, double value);
  inline void add_torque(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      torque() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_torque();

  // @@protoc_insertion_point(class_scope:peripheral.ProtoWrench)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedField< double > force_;
  ::google::protobuf::RepeatedField< double > torque_;
  friend void  protobuf_AddDesc_sensor_2eproto();
  friend void protobuf_AssignDesc_sensor_2eproto();
  friend void protobuf_ShutdownFile_sensor_2eproto();

  void InitAsDefaultInstance();
  static ProtoWrench* default_instance_;
};
// -------------------------------------------------------------------

class ProtoResponseWrench : public ::google::protobuf::Message {
 public:
  ProtoResponseWrench();
  virtual ~ProtoResponseWrench();

  ProtoResponseWrench(const ProtoResponseWrench& from);

  inline ProtoResponseWrench& operator=(const ProtoResponseWrench& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoResponseWrench& default_instance();

  void Swap(ProtoResponseWrench* other);

  // implements Message ----------------------------------------------

  ProtoResponseWrench* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoResponseWrench& from);
  void MergeFrom(const ProtoResponseWrench& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .peripheral.ProtoWrench wrench = 1;
  inline int wrench_size() const;
  inline void clear_wrench();
  static const int kWrenchFieldNumber = 1;
  inline const ::peripheral::ProtoWrench& wrench(int index) const;
  inline ::peripheral::ProtoWrench* mutable_wrench(int index);
  inline ::peripheral::ProtoWrench* add_wrench();
  inline const ::google::protobuf::RepeatedPtrField< ::peripheral::ProtoWrench >&
      wrench() const;
  inline ::google::protobuf::RepeatedPtrField< ::peripheral::ProtoWrench >*
      mutable_wrench();

  // required .aubo.robot.common.RobotCommonResponse errorInfo = 2;
  inline bool has_errorinfo() const;
  inline void clear_errorinfo();
  static const int kErrorInfoFieldNumber = 2;
  inline const ::aubo::robot::common::RobotCommonResponse& errorinfo() const;
  inline ::aubo::robot::common::RobotCommonResponse* mutable_errorinfo();
  inline ::aubo::robot::common::RobotCommonResponse* release_errorinfo();
  inline void set_allocated_errorinfo(::aubo::robot::common::RobotCommonResponse* errorinfo);

  // @@protoc_insertion_point(class_scope:peripheral.ProtoResponseWrench)
 private:
  inline void set_has_errorinfo();
  inline void clear_has_errorinfo();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::peripheral::ProtoWrench > wrench_;
  ::aubo::robot::common::RobotCommonResponse* errorinfo_;
  friend void  protobuf_AddDesc_sensor_2eproto();
  friend void protobuf_AssignDesc_sensor_2eproto();
  friend void protobuf_ShutdownFile_sensor_2eproto();

  void InitAsDefaultInstance();
  static ProtoResponseWrench* default_instance_;
};
// -------------------------------------------------------------------

class ProtoFtSensorCalibParam : public ::google::protobuf::Message {
 public:
  ProtoFtSensorCalibParam();
  virtual ~ProtoFtSensorCalibParam();

  ProtoFtSensorCalibParam(const ProtoFtSensorCalibParam& from);

  inline ProtoFtSensorCalibParam& operator=(const ProtoFtSensorCalibParam& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoFtSensorCalibParam& default_instance();

  void Swap(ProtoFtSensorCalibParam* other);

  // implements Message ----------------------------------------------

  ProtoFtSensorCalibParam* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoFtSensorCalibParam& from);
  void MergeFrom(const ProtoFtSensorCalibParam& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .aubo.robot.common.ProtoJointAngle jointAngle = 1;
  inline int jointangle_size() const;
  inline void clear_jointangle();
  static const int kJointAngleFieldNumber = 1;
  inline const ::aubo::robot::common::ProtoJointAngle& jointangle(int index) const;
  inline ::aubo::robot::common::ProtoJointAngle* mutable_jointangle(int index);
  inline ::aubo::robot::common::ProtoJointAngle* add_jointangle();
  inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ProtoJointAngle >&
      jointangle() const;
  inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ProtoJointAngle >*
      mutable_jointangle();

  // repeated .peripheral.ProtoWrench wrenchList = 2;
  inline int wrenchlist_size() const;
  inline void clear_wrenchlist();
  static const int kWrenchListFieldNumber = 2;
  inline const ::peripheral::ProtoWrench& wrenchlist(int index) const;
  inline ::peripheral::ProtoWrench* mutable_wrenchlist(int index);
  inline ::peripheral::ProtoWrench* add_wrenchlist();
  inline const ::google::protobuf::RepeatedPtrField< ::peripheral::ProtoWrench >&
      wrenchlist() const;
  inline ::google::protobuf::RepeatedPtrField< ::peripheral::ProtoWrench >*
      mutable_wrenchlist();

  // @@protoc_insertion_point(class_scope:peripheral.ProtoFtSensorCalibParam)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ProtoJointAngle > jointangle_;
  ::google::protobuf::RepeatedPtrField< ::peripheral::ProtoWrench > wrenchlist_;
  friend void  protobuf_AddDesc_sensor_2eproto();
  friend void protobuf_AssignDesc_sensor_2eproto();
  friend void protobuf_ShutdownFile_sensor_2eproto();

  void InitAsDefaultInstance();
  static ProtoFtSensorCalibParam* default_instance_;
};
// -------------------------------------------------------------------

class ProtoFtSensorCalibResult : public ::google::protobuf::Message {
 public:
  ProtoFtSensorCalibResult();
  virtual ~ProtoFtSensorCalibResult();

  ProtoFtSensorCalibResult(const ProtoFtSensorCalibResult& from);

  inline ProtoFtSensorCalibResult& operator=(const ProtoFtSensorCalibResult& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoFtSensorCalibResult& default_instance();

  void Swap(ProtoFtSensorCalibResult* other);

  // implements Message ----------------------------------------------

  ProtoFtSensorCalibResult* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoFtSensorCalibResult& from);
  void MergeFrom(const ProtoFtSensorCalibResult& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .peripheral.ProtoWrench wrench = 1;
  inline bool has_wrench() const;
  inline void clear_wrench();
  static const int kWrenchFieldNumber = 1;
  inline const ::peripheral::ProtoWrench& wrench() const;
  inline ::peripheral::ProtoWrench* mutable_wrench();
  inline ::peripheral::ProtoWrench* release_wrench();
  inline void set_allocated_wrench(::peripheral::ProtoWrench* wrench);

  // required double mass = 2;
  inline bool has_mass() const;
  inline void clear_mass();
  static const int kMassFieldNumber = 2;
  inline double mass() const;
  inline void set_mass(double value);

  // repeated double centroid = 3;
  inline int centroid_size() const;
  inline void clear_centroid();
  static const int kCentroidFieldNumber = 3;
  inline double centroid(int index) const;
  inline void set_centroid(int index, double value);
  inline void add_centroid(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      centroid() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_centroid();

  // repeated double angle = 4;
  inline int angle_size() const;
  inline void clear_angle();
  static const int kAngleFieldNumber = 4;
  inline double angle(int index) const;
  inline void set_angle(int index, double value);
  inline void add_angle(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      angle() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_angle();

  // @@protoc_insertion_point(class_scope:peripheral.ProtoFtSensorCalibResult)
 private:
  inline void set_has_wrench();
  inline void clear_has_wrench();
  inline void set_has_mass();
  inline void clear_has_mass();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::peripheral::ProtoWrench* wrench_;
  double mass_;
  ::google::protobuf::RepeatedField< double > centroid_;
  ::google::protobuf::RepeatedField< double > angle_;
  friend void  protobuf_AddDesc_sensor_2eproto();
  friend void protobuf_AssignDesc_sensor_2eproto();
  friend void protobuf_ShutdownFile_sensor_2eproto();

  void InitAsDefaultInstance();
  static ProtoFtSensorCalibResult* default_instance_;
};
// -------------------------------------------------------------------

class ProtoResponseFtSensorCalibResult : public ::google::protobuf::Message {
 public:
  ProtoResponseFtSensorCalibResult();
  virtual ~ProtoResponseFtSensorCalibResult();

  ProtoResponseFtSensorCalibResult(const ProtoResponseFtSensorCalibResult& from);

  inline ProtoResponseFtSensorCalibResult& operator=(const ProtoResponseFtSensorCalibResult& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoResponseFtSensorCalibResult& default_instance();

  void Swap(ProtoResponseFtSensorCalibResult* other);

  // implements Message ----------------------------------------------

  ProtoResponseFtSensorCalibResult* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoResponseFtSensorCalibResult& from);
  void MergeFrom(const ProtoResponseFtSensorCalibResult& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .peripheral.ProtoFtSensorCalibResult ftSensorCalibResult = 1;
  inline int ftsensorcalibresult_size() const;
  inline void clear_ftsensorcalibresult();
  static const int kFtSensorCalibResultFieldNumber = 1;
  inline const ::peripheral::ProtoFtSensorCalibResult& ftsensorcalibresult(int index) const;
  inline ::peripheral::ProtoFtSensorCalibResult* mutable_ftsensorcalibresult(int index);
  inline ::peripheral::ProtoFtSensorCalibResult* add_ftsensorcalibresult();
  inline const ::google::protobuf::RepeatedPtrField< ::peripheral::ProtoFtSensorCalibResult >&
      ftsensorcalibresult() const;
  inline ::google::protobuf::RepeatedPtrField< ::peripheral::ProtoFtSensorCalibResult >*
      mutable_ftsensorcalibresult();

  // required .aubo.robot.common.RobotCommonResponse errorInfo = 2;
  inline bool has_errorinfo() const;
  inline void clear_errorinfo();
  static const int kErrorInfoFieldNumber = 2;
  inline const ::aubo::robot::common::RobotCommonResponse& errorinfo() const;
  inline ::aubo::robot::common::RobotCommonResponse* mutable_errorinfo();
  inline ::aubo::robot::common::RobotCommonResponse* release_errorinfo();
  inline void set_allocated_errorinfo(::aubo::robot::common::RobotCommonResponse* errorinfo);

  // @@protoc_insertion_point(class_scope:peripheral.ProtoResponseFtSensorCalibResult)
 private:
  inline void set_has_errorinfo();
  inline void clear_has_errorinfo();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::peripheral::ProtoFtSensorCalibResult > ftsensorcalibresult_;
  ::aubo::robot::common::RobotCommonResponse* errorinfo_;
  friend void  protobuf_AddDesc_sensor_2eproto();
  friend void protobuf_AssignDesc_sensor_2eproto();
  friend void protobuf_ShutdownFile_sensor_2eproto();

  void InitAsDefaultInstance();
  static ProtoResponseFtSensorCalibResult* default_instance_;
};
// ===================================================================


// ===================================================================

// ProtoWrench

// repeated double force = 1;
inline int ProtoWrench::force_size() const {
  return force_.size();
}
inline void ProtoWrench::clear_force() {
  force_.Clear();
}
inline double ProtoWrench::force(int index) const {
  // @@protoc_insertion_point(field_get:peripheral.ProtoWrench.force)
  return force_.Get(index);
}
inline void ProtoWrench::set_force(int index, double value) {
  force_.Set(index, value);
  // @@protoc_insertion_point(field_set:peripheral.ProtoWrench.force)
}
inline void ProtoWrench::add_force(double value) {
  force_.Add(value);
  // @@protoc_insertion_point(field_add:peripheral.ProtoWrench.force)
}
inline const ::google::protobuf::RepeatedField< double >&
ProtoWrench::force() const {
  // @@protoc_insertion_point(field_list:peripheral.ProtoWrench.force)
  return force_;
}
inline ::google::protobuf::RepeatedField< double >*
ProtoWrench::mutable_force() {
  // @@protoc_insertion_point(field_mutable_list:peripheral.ProtoWrench.force)
  return &force_;
}

// repeated double torque = 2;
inline int ProtoWrench::torque_size() const {
  return torque_.size();
}
inline void ProtoWrench::clear_torque() {
  torque_.Clear();
}
inline double ProtoWrench::torque(int index) const {
  // @@protoc_insertion_point(field_get:peripheral.ProtoWrench.torque)
  return torque_.Get(index);
}
inline void ProtoWrench::set_torque(int index, double value) {
  torque_.Set(index, value);
  // @@protoc_insertion_point(field_set:peripheral.ProtoWrench.torque)
}
inline void ProtoWrench::add_torque(double value) {
  torque_.Add(value);
  // @@protoc_insertion_point(field_add:peripheral.ProtoWrench.torque)
}
inline const ::google::protobuf::RepeatedField< double >&
ProtoWrench::torque() const {
  // @@protoc_insertion_point(field_list:peripheral.ProtoWrench.torque)
  return torque_;
}
inline ::google::protobuf::RepeatedField< double >*
ProtoWrench::mutable_torque() {
  // @@protoc_insertion_point(field_mutable_list:peripheral.ProtoWrench.torque)
  return &torque_;
}

// -------------------------------------------------------------------

// ProtoResponseWrench

// repeated .peripheral.ProtoWrench wrench = 1;
inline int ProtoResponseWrench::wrench_size() const {
  return wrench_.size();
}
inline void ProtoResponseWrench::clear_wrench() {
  wrench_.Clear();
}
inline const ::peripheral::ProtoWrench& ProtoResponseWrench::wrench(int index) const {
  // @@protoc_insertion_point(field_get:peripheral.ProtoResponseWrench.wrench)
  return wrench_.Get(index);
}
inline ::peripheral::ProtoWrench* ProtoResponseWrench::mutable_wrench(int index) {
  // @@protoc_insertion_point(field_mutable:peripheral.ProtoResponseWrench.wrench)
  return wrench_.Mutable(index);
}
inline ::peripheral::ProtoWrench* ProtoResponseWrench::add_wrench() {
  // @@protoc_insertion_point(field_add:peripheral.ProtoResponseWrench.wrench)
  return wrench_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::peripheral::ProtoWrench >&
ProtoResponseWrench::wrench() const {
  // @@protoc_insertion_point(field_list:peripheral.ProtoResponseWrench.wrench)
  return wrench_;
}
inline ::google::protobuf::RepeatedPtrField< ::peripheral::ProtoWrench >*
ProtoResponseWrench::mutable_wrench() {
  // @@protoc_insertion_point(field_mutable_list:peripheral.ProtoResponseWrench.wrench)
  return &wrench_;
}

// required .aubo.robot.common.RobotCommonResponse errorInfo = 2;
inline bool ProtoResponseWrench::has_errorinfo() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoResponseWrench::set_has_errorinfo() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoResponseWrench::clear_has_errorinfo() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoResponseWrench::clear_errorinfo() {
  if (errorinfo_ != NULL) errorinfo_->::aubo::robot::common::RobotCommonResponse::Clear();
  clear_has_errorinfo();
}
inline const ::aubo::robot::common::RobotCommonResponse& ProtoResponseWrench::errorinfo() const {
  // @@protoc_insertion_point(field_get:peripheral.ProtoResponseWrench.errorInfo)
  return errorinfo_ != NULL ? *errorinfo_ : *default_instance_->errorinfo_;
}
inline ::aubo::robot::common::RobotCommonResponse* ProtoResponseWrench::mutable_errorinfo() {
  set_has_errorinfo();
  if (errorinfo_ == NULL) errorinfo_ = new ::aubo::robot::common::RobotCommonResponse;
  // @@protoc_insertion_point(field_mutable:peripheral.ProtoResponseWrench.errorInfo)
  return errorinfo_;
}
inline ::aubo::robot::common::RobotCommonResponse* ProtoResponseWrench::release_errorinfo() {
  clear_has_errorinfo();
  ::aubo::robot::common::RobotCommonResponse* temp = errorinfo_;
  errorinfo_ = NULL;
  return temp;
}
inline void ProtoResponseWrench::set_allocated_errorinfo(::aubo::robot::common::RobotCommonResponse* errorinfo) {
  delete errorinfo_;
  errorinfo_ = errorinfo;
  if (errorinfo) {
    set_has_errorinfo();
  } else {
    clear_has_errorinfo();
  }
  // @@protoc_insertion_point(field_set_allocated:peripheral.ProtoResponseWrench.errorInfo)
}

// -------------------------------------------------------------------

// ProtoFtSensorCalibParam

// repeated .aubo.robot.common.ProtoJointAngle jointAngle = 1;
inline int ProtoFtSensorCalibParam::jointangle_size() const {
  return jointangle_.size();
}
inline void ProtoFtSensorCalibParam::clear_jointangle() {
  jointangle_.Clear();
}
inline const ::aubo::robot::common::ProtoJointAngle& ProtoFtSensorCalibParam::jointangle(int index) const {
  // @@protoc_insertion_point(field_get:peripheral.ProtoFtSensorCalibParam.jointAngle)
  return jointangle_.Get(index);
}
inline ::aubo::robot::common::ProtoJointAngle* ProtoFtSensorCalibParam::mutable_jointangle(int index) {
  // @@protoc_insertion_point(field_mutable:peripheral.ProtoFtSensorCalibParam.jointAngle)
  return jointangle_.Mutable(index);
}
inline ::aubo::robot::common::ProtoJointAngle* ProtoFtSensorCalibParam::add_jointangle() {
  // @@protoc_insertion_point(field_add:peripheral.ProtoFtSensorCalibParam.jointAngle)
  return jointangle_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ProtoJointAngle >&
ProtoFtSensorCalibParam::jointangle() const {
  // @@protoc_insertion_point(field_list:peripheral.ProtoFtSensorCalibParam.jointAngle)
  return jointangle_;
}
inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ProtoJointAngle >*
ProtoFtSensorCalibParam::mutable_jointangle() {
  // @@protoc_insertion_point(field_mutable_list:peripheral.ProtoFtSensorCalibParam.jointAngle)
  return &jointangle_;
}

// repeated .peripheral.ProtoWrench wrenchList = 2;
inline int ProtoFtSensorCalibParam::wrenchlist_size() const {
  return wrenchlist_.size();
}
inline void ProtoFtSensorCalibParam::clear_wrenchlist() {
  wrenchlist_.Clear();
}
inline const ::peripheral::ProtoWrench& ProtoFtSensorCalibParam::wrenchlist(int index) const {
  // @@protoc_insertion_point(field_get:peripheral.ProtoFtSensorCalibParam.wrenchList)
  return wrenchlist_.Get(index);
}
inline ::peripheral::ProtoWrench* ProtoFtSensorCalibParam::mutable_wrenchlist(int index) {
  // @@protoc_insertion_point(field_mutable:peripheral.ProtoFtSensorCalibParam.wrenchList)
  return wrenchlist_.Mutable(index);
}
inline ::peripheral::ProtoWrench* ProtoFtSensorCalibParam::add_wrenchlist() {
  // @@protoc_insertion_point(field_add:peripheral.ProtoFtSensorCalibParam.wrenchList)
  return wrenchlist_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::peripheral::ProtoWrench >&
ProtoFtSensorCalibParam::wrenchlist() const {
  // @@protoc_insertion_point(field_list:peripheral.ProtoFtSensorCalibParam.wrenchList)
  return wrenchlist_;
}
inline ::google::protobuf::RepeatedPtrField< ::peripheral::ProtoWrench >*
ProtoFtSensorCalibParam::mutable_wrenchlist() {
  // @@protoc_insertion_point(field_mutable_list:peripheral.ProtoFtSensorCalibParam.wrenchList)
  return &wrenchlist_;
}

// -------------------------------------------------------------------

// ProtoFtSensorCalibResult

// required .peripheral.ProtoWrench wrench = 1;
inline bool ProtoFtSensorCalibResult::has_wrench() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoFtSensorCalibResult::set_has_wrench() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoFtSensorCalibResult::clear_has_wrench() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoFtSensorCalibResult::clear_wrench() {
  if (wrench_ != NULL) wrench_->::peripheral::ProtoWrench::Clear();
  clear_has_wrench();
}
inline const ::peripheral::ProtoWrench& ProtoFtSensorCalibResult::wrench() const {
  // @@protoc_insertion_point(field_get:peripheral.ProtoFtSensorCalibResult.wrench)
  return wrench_ != NULL ? *wrench_ : *default_instance_->wrench_;
}
inline ::peripheral::ProtoWrench* ProtoFtSensorCalibResult::mutable_wrench() {
  set_has_wrench();
  if (wrench_ == NULL) wrench_ = new ::peripheral::ProtoWrench;
  // @@protoc_insertion_point(field_mutable:peripheral.ProtoFtSensorCalibResult.wrench)
  return wrench_;
}
inline ::peripheral::ProtoWrench* ProtoFtSensorCalibResult::release_wrench() {
  clear_has_wrench();
  ::peripheral::ProtoWrench* temp = wrench_;
  wrench_ = NULL;
  return temp;
}
inline void ProtoFtSensorCalibResult::set_allocated_wrench(::peripheral::ProtoWrench* wrench) {
  delete wrench_;
  wrench_ = wrench;
  if (wrench) {
    set_has_wrench();
  } else {
    clear_has_wrench();
  }
  // @@protoc_insertion_point(field_set_allocated:peripheral.ProtoFtSensorCalibResult.wrench)
}

// required double mass = 2;
inline bool ProtoFtSensorCalibResult::has_mass() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoFtSensorCalibResult::set_has_mass() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoFtSensorCalibResult::clear_has_mass() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoFtSensorCalibResult::clear_mass() {
  mass_ = 0;
  clear_has_mass();
}
inline double ProtoFtSensorCalibResult::mass() const {
  // @@protoc_insertion_point(field_get:peripheral.ProtoFtSensorCalibResult.mass)
  return mass_;
}
inline void ProtoFtSensorCalibResult::set_mass(double value) {
  set_has_mass();
  mass_ = value;
  // @@protoc_insertion_point(field_set:peripheral.ProtoFtSensorCalibResult.mass)
}

// repeated double centroid = 3;
inline int ProtoFtSensorCalibResult::centroid_size() const {
  return centroid_.size();
}
inline void ProtoFtSensorCalibResult::clear_centroid() {
  centroid_.Clear();
}
inline double ProtoFtSensorCalibResult::centroid(int index) const {
  // @@protoc_insertion_point(field_get:peripheral.ProtoFtSensorCalibResult.centroid)
  return centroid_.Get(index);
}
inline void ProtoFtSensorCalibResult::set_centroid(int index, double value) {
  centroid_.Set(index, value);
  // @@protoc_insertion_point(field_set:peripheral.ProtoFtSensorCalibResult.centroid)
}
inline void ProtoFtSensorCalibResult::add_centroid(double value) {
  centroid_.Add(value);
  // @@protoc_insertion_point(field_add:peripheral.ProtoFtSensorCalibResult.centroid)
}
inline const ::google::protobuf::RepeatedField< double >&
ProtoFtSensorCalibResult::centroid() const {
  // @@protoc_insertion_point(field_list:peripheral.ProtoFtSensorCalibResult.centroid)
  return centroid_;
}
inline ::google::protobuf::RepeatedField< double >*
ProtoFtSensorCalibResult::mutable_centroid() {
  // @@protoc_insertion_point(field_mutable_list:peripheral.ProtoFtSensorCalibResult.centroid)
  return &centroid_;
}

// repeated double angle = 4;
inline int ProtoFtSensorCalibResult::angle_size() const {
  return angle_.size();
}
inline void ProtoFtSensorCalibResult::clear_angle() {
  angle_.Clear();
}
inline double ProtoFtSensorCalibResult::angle(int index) const {
  // @@protoc_insertion_point(field_get:peripheral.ProtoFtSensorCalibResult.angle)
  return angle_.Get(index);
}
inline void ProtoFtSensorCalibResult::set_angle(int index, double value) {
  angle_.Set(index, value);
  // @@protoc_insertion_point(field_set:peripheral.ProtoFtSensorCalibResult.angle)
}
inline void ProtoFtSensorCalibResult::add_angle(double value) {
  angle_.Add(value);
  // @@protoc_insertion_point(field_add:peripheral.ProtoFtSensorCalibResult.angle)
}
inline const ::google::protobuf::RepeatedField< double >&
ProtoFtSensorCalibResult::angle() const {
  // @@protoc_insertion_point(field_list:peripheral.ProtoFtSensorCalibResult.angle)
  return angle_;
}
inline ::google::protobuf::RepeatedField< double >*
ProtoFtSensorCalibResult::mutable_angle() {
  // @@protoc_insertion_point(field_mutable_list:peripheral.ProtoFtSensorCalibResult.angle)
  return &angle_;
}

// -------------------------------------------------------------------

// ProtoResponseFtSensorCalibResult

// repeated .peripheral.ProtoFtSensorCalibResult ftSensorCalibResult = 1;
inline int ProtoResponseFtSensorCalibResult::ftsensorcalibresult_size() const {
  return ftsensorcalibresult_.size();
}
inline void ProtoResponseFtSensorCalibResult::clear_ftsensorcalibresult() {
  ftsensorcalibresult_.Clear();
}
inline const ::peripheral::ProtoFtSensorCalibResult& ProtoResponseFtSensorCalibResult::ftsensorcalibresult(int index) const {
  // @@protoc_insertion_point(field_get:peripheral.ProtoResponseFtSensorCalibResult.ftSensorCalibResult)
  return ftsensorcalibresult_.Get(index);
}
inline ::peripheral::ProtoFtSensorCalibResult* ProtoResponseFtSensorCalibResult::mutable_ftsensorcalibresult(int index) {
  // @@protoc_insertion_point(field_mutable:peripheral.ProtoResponseFtSensorCalibResult.ftSensorCalibResult)
  return ftsensorcalibresult_.Mutable(index);
}
inline ::peripheral::ProtoFtSensorCalibResult* ProtoResponseFtSensorCalibResult::add_ftsensorcalibresult() {
  // @@protoc_insertion_point(field_add:peripheral.ProtoResponseFtSensorCalibResult.ftSensorCalibResult)
  return ftsensorcalibresult_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::peripheral::ProtoFtSensorCalibResult >&
ProtoResponseFtSensorCalibResult::ftsensorcalibresult() const {
  // @@protoc_insertion_point(field_list:peripheral.ProtoResponseFtSensorCalibResult.ftSensorCalibResult)
  return ftsensorcalibresult_;
}
inline ::google::protobuf::RepeatedPtrField< ::peripheral::ProtoFtSensorCalibResult >*
ProtoResponseFtSensorCalibResult::mutable_ftsensorcalibresult() {
  // @@protoc_insertion_point(field_mutable_list:peripheral.ProtoResponseFtSensorCalibResult.ftSensorCalibResult)
  return &ftsensorcalibresult_;
}

// required .aubo.robot.common.RobotCommonResponse errorInfo = 2;
inline bool ProtoResponseFtSensorCalibResult::has_errorinfo() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoResponseFtSensorCalibResult::set_has_errorinfo() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoResponseFtSensorCalibResult::clear_has_errorinfo() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoResponseFtSensorCalibResult::clear_errorinfo() {
  if (errorinfo_ != NULL) errorinfo_->::aubo::robot::common::RobotCommonResponse::Clear();
  clear_has_errorinfo();
}
inline const ::aubo::robot::common::RobotCommonResponse& ProtoResponseFtSensorCalibResult::errorinfo() const {
  // @@protoc_insertion_point(field_get:peripheral.ProtoResponseFtSensorCalibResult.errorInfo)
  return errorinfo_ != NULL ? *errorinfo_ : *default_instance_->errorinfo_;
}
inline ::aubo::robot::common::RobotCommonResponse* ProtoResponseFtSensorCalibResult::mutable_errorinfo() {
  set_has_errorinfo();
  if (errorinfo_ == NULL) errorinfo_ = new ::aubo::robot::common::RobotCommonResponse;
  // @@protoc_insertion_point(field_mutable:peripheral.ProtoResponseFtSensorCalibResult.errorInfo)
  return errorinfo_;
}
inline ::aubo::robot::common::RobotCommonResponse* ProtoResponseFtSensorCalibResult::release_errorinfo() {
  clear_has_errorinfo();
  ::aubo::robot::common::RobotCommonResponse* temp = errorinfo_;
  errorinfo_ = NULL;
  return temp;
}
inline void ProtoResponseFtSensorCalibResult::set_allocated_errorinfo(::aubo::robot::common::RobotCommonResponse* errorinfo) {
  delete errorinfo_;
  errorinfo_ = errorinfo;
  if (errorinfo) {
    set_has_errorinfo();
  } else {
    clear_has_errorinfo();
  }
  // @@protoc_insertion_point(field_set_allocated:peripheral.ProtoResponseFtSensorCalibResult.errorInfo)
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace peripheral

#ifndef SWIG
namespace google {
namespace protobuf {


}  // namespace google
}  // namespace protobuf
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_sensor_2eproto__INCLUDED
