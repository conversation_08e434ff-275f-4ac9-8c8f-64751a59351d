// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: robotcommon.proto

#ifndef PROTOBUF_robotcommon_2eproto__INCLUDED
#define PROTOBUF_robotcommon_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 2006000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 2006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "robotmovecondition.pb.h"
#include "robotcommunication.pb.h"
// @@protoc_insertion_point(includes)

namespace aubo {
namespace robot {
namespace common {

// Internal implementation detail -- do not call these.
void  protobuf_AddDesc_robotcommon_2eproto();
void protobuf_AssignDesc_robotcommon_2eproto();
void protobuf_ShutdownFile_robotcommon_2eproto();

class Pos;
class cartesianPos_U;
class Ori;
class cartesianOri_U;
class ProtoRoadPoint;
class ProtoRoadPointResponse;
class ProtoJointAngle;
class ProtoJointAngleResponse;
class ProtoForceSensorData;
class ProtoForceSensorDataResponse;
class ProtoJointStatus;
class ProtoRobotAllJointStatusResponse;
class relativeMove_t;
class joint_cart_U;
class arrivalAhead_t;
class ProtoToolInEndDesc;
class RobotMoveProfile;
class RobotMove;
class RobotTeachMove;
class RobotCommonResponse;
class ProtoResponseRobotTcpParam;
class ProtoResponseRobotGravityComponent;
class ProtoResponseRobotCollisionCurrent;
class ProtoResponseRobotDevInfo;
class ToolInertia;
class ToolDynamicsParam;
class ToolKinematicsParam;
class ToolParam;
class ProtoConveyorTrackValuePoint;
class ProtoRobotSafetyConfig;
class ProtoResponseRobotSafetyConfig;
class ProtoOrpeSafetyStatus;
class ProtoResponseOrpeSafetyStatus;
class DhParam;
class ProtoResponseRobotDhParam;
class RobotJointOffset;
class ProtoRobotMoveFuncResult;
class ProtoSeamTrack_t;
class ProtoSeamTrackResponse_t;

// ===================================================================

class Pos : public ::google::protobuf::Message {
 public:
  Pos();
  virtual ~Pos();

  Pos(const Pos& from);

  inline Pos& operator=(const Pos& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const Pos& default_instance();

  void Swap(Pos* other);

  // implements Message ----------------------------------------------

  Pos* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const Pos& from);
  void MergeFrom(const Pos& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required double x = 1;
  inline bool has_x() const;
  inline void clear_x();
  static const int kXFieldNumber = 1;
  inline double x() const;
  inline void set_x(double value);

  // required double y = 2;
  inline bool has_y() const;
  inline void clear_y();
  static const int kYFieldNumber = 2;
  inline double y() const;
  inline void set_y(double value);

  // required double z = 3;
  inline bool has_z() const;
  inline void clear_z();
  static const int kZFieldNumber = 3;
  inline double z() const;
  inline void set_z(double value);

  // @@protoc_insertion_point(class_scope:aubo.robot.common.Pos)
 private:
  inline void set_has_x();
  inline void clear_has_x();
  inline void set_has_y();
  inline void clear_has_y();
  inline void set_has_z();
  inline void clear_has_z();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  double x_;
  double y_;
  double z_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static Pos* default_instance_;
};
// -------------------------------------------------------------------

class cartesianPos_U : public ::google::protobuf::Message {
 public:
  cartesianPos_U();
  virtual ~cartesianPos_U();

  cartesianPos_U(const cartesianPos_U& from);

  inline cartesianPos_U& operator=(const cartesianPos_U& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const cartesianPos_U& default_instance();

  void Swap(cartesianPos_U* other);

  // implements Message ----------------------------------------------

  cartesianPos_U* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const cartesianPos_U& from);
  void MergeFrom(const cartesianPos_U& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .aubo.robot.common.Pos position = 1;
  inline int position_size() const;
  inline void clear_position();
  static const int kPositionFieldNumber = 1;
  inline const ::aubo::robot::common::Pos& position(int index) const;
  inline ::aubo::robot::common::Pos* mutable_position(int index);
  inline ::aubo::robot::common::Pos* add_position();
  inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::Pos >&
      position() const;
  inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::Pos >*
      mutable_position();

  // repeated double positionVector = 2;
  inline int positionvector_size() const;
  inline void clear_positionvector();
  static const int kPositionVectorFieldNumber = 2;
  inline double positionvector(int index) const;
  inline void set_positionvector(int index, double value);
  inline void add_positionvector(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      positionvector() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_positionvector();

  // @@protoc_insertion_point(class_scope:aubo.robot.common.cartesianPos_U)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::Pos > position_;
  ::google::protobuf::RepeatedField< double > positionvector_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static cartesianPos_U* default_instance_;
};
// -------------------------------------------------------------------

class Ori : public ::google::protobuf::Message {
 public:
  Ori();
  virtual ~Ori();

  Ori(const Ori& from);

  inline Ori& operator=(const Ori& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const Ori& default_instance();

  void Swap(Ori* other);

  // implements Message ----------------------------------------------

  Ori* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const Ori& from);
  void MergeFrom(const Ori& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required double w = 1;
  inline bool has_w() const;
  inline void clear_w();
  static const int kWFieldNumber = 1;
  inline double w() const;
  inline void set_w(double value);

  // required double x = 2;
  inline bool has_x() const;
  inline void clear_x();
  static const int kXFieldNumber = 2;
  inline double x() const;
  inline void set_x(double value);

  // required double y = 3;
  inline bool has_y() const;
  inline void clear_y();
  static const int kYFieldNumber = 3;
  inline double y() const;
  inline void set_y(double value);

  // required double z = 4;
  inline bool has_z() const;
  inline void clear_z();
  static const int kZFieldNumber = 4;
  inline double z() const;
  inline void set_z(double value);

  // @@protoc_insertion_point(class_scope:aubo.robot.common.Ori)
 private:
  inline void set_has_w();
  inline void clear_has_w();
  inline void set_has_x();
  inline void clear_has_x();
  inline void set_has_y();
  inline void clear_has_y();
  inline void set_has_z();
  inline void clear_has_z();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  double w_;
  double x_;
  double y_;
  double z_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static Ori* default_instance_;
};
// -------------------------------------------------------------------

class cartesianOri_U : public ::google::protobuf::Message {
 public:
  cartesianOri_U();
  virtual ~cartesianOri_U();

  cartesianOri_U(const cartesianOri_U& from);

  inline cartesianOri_U& operator=(const cartesianOri_U& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const cartesianOri_U& default_instance();

  void Swap(cartesianOri_U* other);

  // implements Message ----------------------------------------------

  cartesianOri_U* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const cartesianOri_U& from);
  void MergeFrom(const cartesianOri_U& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .aubo.robot.common.Ori orientation = 1;
  inline int orientation_size() const;
  inline void clear_orientation();
  static const int kOrientationFieldNumber = 1;
  inline const ::aubo::robot::common::Ori& orientation(int index) const;
  inline ::aubo::robot::common::Ori* mutable_orientation(int index);
  inline ::aubo::robot::common::Ori* add_orientation();
  inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::Ori >&
      orientation() const;
  inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::Ori >*
      mutable_orientation();

  // repeated double quaternionVector = 2;
  inline int quaternionvector_size() const;
  inline void clear_quaternionvector();
  static const int kQuaternionVectorFieldNumber = 2;
  inline double quaternionvector(int index) const;
  inline void set_quaternionvector(int index, double value);
  inline void add_quaternionvector(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      quaternionvector() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_quaternionvector();

  // @@protoc_insertion_point(class_scope:aubo.robot.common.cartesianOri_U)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::Ori > orientation_;
  ::google::protobuf::RepeatedField< double > quaternionvector_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static cartesianOri_U* default_instance_;
};
// -------------------------------------------------------------------

class ProtoRoadPoint : public ::google::protobuf::Message {
 public:
  ProtoRoadPoint();
  virtual ~ProtoRoadPoint();

  ProtoRoadPoint(const ProtoRoadPoint& from);

  inline ProtoRoadPoint& operator=(const ProtoRoadPoint& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoRoadPoint& default_instance();

  void Swap(ProtoRoadPoint* other);

  // implements Message ----------------------------------------------

  ProtoRoadPoint* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoRoadPoint& from);
  void MergeFrom(const ProtoRoadPoint& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .aubo.robot.common.cartesianPos_U cartPos = 1;
  inline bool has_cartpos() const;
  inline void clear_cartpos();
  static const int kCartPosFieldNumber = 1;
  inline const ::aubo::robot::common::cartesianPos_U& cartpos() const;
  inline ::aubo::robot::common::cartesianPos_U* mutable_cartpos();
  inline ::aubo::robot::common::cartesianPos_U* release_cartpos();
  inline void set_allocated_cartpos(::aubo::robot::common::cartesianPos_U* cartpos);

  // required .aubo.robot.common.Ori orientation = 2;
  inline bool has_orientation() const;
  inline void clear_orientation();
  static const int kOrientationFieldNumber = 2;
  inline const ::aubo::robot::common::Ori& orientation() const;
  inline ::aubo::robot::common::Ori* mutable_orientation();
  inline ::aubo::robot::common::Ori* release_orientation();
  inline void set_allocated_orientation(::aubo::robot::common::Ori* orientation);

  // repeated double jointpos = 3;
  inline int jointpos_size() const;
  inline void clear_jointpos();
  static const int kJointposFieldNumber = 3;
  inline double jointpos(int index) const;
  inline void set_jointpos(int index, double value);
  inline void add_jointpos(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      jointpos() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_jointpos();

  // @@protoc_insertion_point(class_scope:aubo.robot.common.ProtoRoadPoint)
 private:
  inline void set_has_cartpos();
  inline void clear_has_cartpos();
  inline void set_has_orientation();
  inline void clear_has_orientation();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::aubo::robot::common::cartesianPos_U* cartpos_;
  ::aubo::robot::common::Ori* orientation_;
  ::google::protobuf::RepeatedField< double > jointpos_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static ProtoRoadPoint* default_instance_;
};
// -------------------------------------------------------------------

class ProtoRoadPointResponse : public ::google::protobuf::Message {
 public:
  ProtoRoadPointResponse();
  virtual ~ProtoRoadPointResponse();

  ProtoRoadPointResponse(const ProtoRoadPointResponse& from);

  inline ProtoRoadPointResponse& operator=(const ProtoRoadPointResponse& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoRoadPointResponse& default_instance();

  void Swap(ProtoRoadPointResponse* other);

  // implements Message ----------------------------------------------

  ProtoRoadPointResponse* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoRoadPointResponse& from);
  void MergeFrom(const ProtoRoadPointResponse& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .aubo.robot.common.ProtoRoadPoint waypoint = 1;
  inline bool has_waypoint() const;
  inline void clear_waypoint();
  static const int kWaypointFieldNumber = 1;
  inline const ::aubo::robot::common::ProtoRoadPoint& waypoint() const;
  inline ::aubo::robot::common::ProtoRoadPoint* mutable_waypoint();
  inline ::aubo::robot::common::ProtoRoadPoint* release_waypoint();
  inline void set_allocated_waypoint(::aubo::robot::common::ProtoRoadPoint* waypoint);

  // repeated .aubo.robot.common.RobotCommonResponse errorInfo = 2;
  inline int errorinfo_size() const;
  inline void clear_errorinfo();
  static const int kErrorInfoFieldNumber = 2;
  inline const ::aubo::robot::common::RobotCommonResponse& errorinfo(int index) const;
  inline ::aubo::robot::common::RobotCommonResponse* mutable_errorinfo(int index);
  inline ::aubo::robot::common::RobotCommonResponse* add_errorinfo();
  inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::RobotCommonResponse >&
      errorinfo() const;
  inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::RobotCommonResponse >*
      mutable_errorinfo();

  // @@protoc_insertion_point(class_scope:aubo.robot.common.ProtoRoadPointResponse)
 private:
  inline void set_has_waypoint();
  inline void clear_has_waypoint();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::aubo::robot::common::ProtoRoadPoint* waypoint_;
  ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::RobotCommonResponse > errorinfo_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static ProtoRoadPointResponse* default_instance_;
};
// -------------------------------------------------------------------

class ProtoJointAngle : public ::google::protobuf::Message {
 public:
  ProtoJointAngle();
  virtual ~ProtoJointAngle();

  ProtoJointAngle(const ProtoJointAngle& from);

  inline ProtoJointAngle& operator=(const ProtoJointAngle& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoJointAngle& default_instance();

  void Swap(ProtoJointAngle* other);

  // implements Message ----------------------------------------------

  ProtoJointAngle* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoJointAngle& from);
  void MergeFrom(const ProtoJointAngle& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required double joint1 = 1;
  inline bool has_joint1() const;
  inline void clear_joint1();
  static const int kJoint1FieldNumber = 1;
  inline double joint1() const;
  inline void set_joint1(double value);

  // required double joint2 = 2;
  inline bool has_joint2() const;
  inline void clear_joint2();
  static const int kJoint2FieldNumber = 2;
  inline double joint2() const;
  inline void set_joint2(double value);

  // required double joint3 = 3;
  inline bool has_joint3() const;
  inline void clear_joint3();
  static const int kJoint3FieldNumber = 3;
  inline double joint3() const;
  inline void set_joint3(double value);

  // required double joint4 = 4;
  inline bool has_joint4() const;
  inline void clear_joint4();
  static const int kJoint4FieldNumber = 4;
  inline double joint4() const;
  inline void set_joint4(double value);

  // required double joint5 = 5;
  inline bool has_joint5() const;
  inline void clear_joint5();
  static const int kJoint5FieldNumber = 5;
  inline double joint5() const;
  inline void set_joint5(double value);

  // required double joint6 = 6;
  inline bool has_joint6() const;
  inline void clear_joint6();
  static const int kJoint6FieldNumber = 6;
  inline double joint6() const;
  inline void set_joint6(double value);

  // @@protoc_insertion_point(class_scope:aubo.robot.common.ProtoJointAngle)
 private:
  inline void set_has_joint1();
  inline void clear_has_joint1();
  inline void set_has_joint2();
  inline void clear_has_joint2();
  inline void set_has_joint3();
  inline void clear_has_joint3();
  inline void set_has_joint4();
  inline void clear_has_joint4();
  inline void set_has_joint5();
  inline void clear_has_joint5();
  inline void set_has_joint6();
  inline void clear_has_joint6();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  double joint1_;
  double joint2_;
  double joint3_;
  double joint4_;
  double joint5_;
  double joint6_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static ProtoJointAngle* default_instance_;
};
// -------------------------------------------------------------------

class ProtoJointAngleResponse : public ::google::protobuf::Message {
 public:
  ProtoJointAngleResponse();
  virtual ~ProtoJointAngleResponse();

  ProtoJointAngleResponse(const ProtoJointAngleResponse& from);

  inline ProtoJointAngleResponse& operator=(const ProtoJointAngleResponse& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoJointAngleResponse& default_instance();

  void Swap(ProtoJointAngleResponse* other);

  // implements Message ----------------------------------------------

  ProtoJointAngleResponse* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoJointAngleResponse& from);
  void MergeFrom(const ProtoJointAngleResponse& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .aubo.robot.common.ProtoJointAngle jointAngle = 1;
  inline bool has_jointangle() const;
  inline void clear_jointangle();
  static const int kJointAngleFieldNumber = 1;
  inline const ::aubo::robot::common::ProtoJointAngle& jointangle() const;
  inline ::aubo::robot::common::ProtoJointAngle* mutable_jointangle();
  inline ::aubo::robot::common::ProtoJointAngle* release_jointangle();
  inline void set_allocated_jointangle(::aubo::robot::common::ProtoJointAngle* jointangle);

  // repeated .aubo.robot.common.RobotCommonResponse errorInfo = 2;
  inline int errorinfo_size() const;
  inline void clear_errorinfo();
  static const int kErrorInfoFieldNumber = 2;
  inline const ::aubo::robot::common::RobotCommonResponse& errorinfo(int index) const;
  inline ::aubo::robot::common::RobotCommonResponse* mutable_errorinfo(int index);
  inline ::aubo::robot::common::RobotCommonResponse* add_errorinfo();
  inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::RobotCommonResponse >&
      errorinfo() const;
  inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::RobotCommonResponse >*
      mutable_errorinfo();

  // @@protoc_insertion_point(class_scope:aubo.robot.common.ProtoJointAngleResponse)
 private:
  inline void set_has_jointangle();
  inline void clear_has_jointangle();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::aubo::robot::common::ProtoJointAngle* jointangle_;
  ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::RobotCommonResponse > errorinfo_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static ProtoJointAngleResponse* default_instance_;
};
// -------------------------------------------------------------------

class ProtoForceSensorData : public ::google::protobuf::Message {
 public:
  ProtoForceSensorData();
  virtual ~ProtoForceSensorData();

  ProtoForceSensorData(const ProtoForceSensorData& from);

  inline ProtoForceSensorData& operator=(const ProtoForceSensorData& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoForceSensorData& default_instance();

  void Swap(ProtoForceSensorData* other);

  // implements Message ----------------------------------------------

  ProtoForceSensorData* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoForceSensorData& from);
  void MergeFrom(const ProtoForceSensorData& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required double data1 = 1;
  inline bool has_data1() const;
  inline void clear_data1();
  static const int kData1FieldNumber = 1;
  inline double data1() const;
  inline void set_data1(double value);

  // required double data2 = 2;
  inline bool has_data2() const;
  inline void clear_data2();
  static const int kData2FieldNumber = 2;
  inline double data2() const;
  inline void set_data2(double value);

  // required double data3 = 3;
  inline bool has_data3() const;
  inline void clear_data3();
  static const int kData3FieldNumber = 3;
  inline double data3() const;
  inline void set_data3(double value);

  // required double data4 = 4;
  inline bool has_data4() const;
  inline void clear_data4();
  static const int kData4FieldNumber = 4;
  inline double data4() const;
  inline void set_data4(double value);

  // required double data5 = 5;
  inline bool has_data5() const;
  inline void clear_data5();
  static const int kData5FieldNumber = 5;
  inline double data5() const;
  inline void set_data5(double value);

  // required double data6 = 6;
  inline bool has_data6() const;
  inline void clear_data6();
  static const int kData6FieldNumber = 6;
  inline double data6() const;
  inline void set_data6(double value);

  // @@protoc_insertion_point(class_scope:aubo.robot.common.ProtoForceSensorData)
 private:
  inline void set_has_data1();
  inline void clear_has_data1();
  inline void set_has_data2();
  inline void clear_has_data2();
  inline void set_has_data3();
  inline void clear_has_data3();
  inline void set_has_data4();
  inline void clear_has_data4();
  inline void set_has_data5();
  inline void clear_has_data5();
  inline void set_has_data6();
  inline void clear_has_data6();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  double data1_;
  double data2_;
  double data3_;
  double data4_;
  double data5_;
  double data6_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static ProtoForceSensorData* default_instance_;
};
// -------------------------------------------------------------------

class ProtoForceSensorDataResponse : public ::google::protobuf::Message {
 public:
  ProtoForceSensorDataResponse();
  virtual ~ProtoForceSensorDataResponse();

  ProtoForceSensorDataResponse(const ProtoForceSensorDataResponse& from);

  inline ProtoForceSensorDataResponse& operator=(const ProtoForceSensorDataResponse& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoForceSensorDataResponse& default_instance();

  void Swap(ProtoForceSensorDataResponse* other);

  // implements Message ----------------------------------------------

  ProtoForceSensorDataResponse* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoForceSensorDataResponse& from);
  void MergeFrom(const ProtoForceSensorDataResponse& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .aubo.robot.common.ProtoForceSensorData forceSensorData = 1;
  inline bool has_forcesensordata() const;
  inline void clear_forcesensordata();
  static const int kForceSensorDataFieldNumber = 1;
  inline const ::aubo::robot::common::ProtoForceSensorData& forcesensordata() const;
  inline ::aubo::robot::common::ProtoForceSensorData* mutable_forcesensordata();
  inline ::aubo::robot::common::ProtoForceSensorData* release_forcesensordata();
  inline void set_allocated_forcesensordata(::aubo::robot::common::ProtoForceSensorData* forcesensordata);

  // repeated .aubo.robot.common.RobotCommonResponse errorInfo = 2;
  inline int errorinfo_size() const;
  inline void clear_errorinfo();
  static const int kErrorInfoFieldNumber = 2;
  inline const ::aubo::robot::common::RobotCommonResponse& errorinfo(int index) const;
  inline ::aubo::robot::common::RobotCommonResponse* mutable_errorinfo(int index);
  inline ::aubo::robot::common::RobotCommonResponse* add_errorinfo();
  inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::RobotCommonResponse >&
      errorinfo() const;
  inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::RobotCommonResponse >*
      mutable_errorinfo();

  // @@protoc_insertion_point(class_scope:aubo.robot.common.ProtoForceSensorDataResponse)
 private:
  inline void set_has_forcesensordata();
  inline void clear_has_forcesensordata();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::aubo::robot::common::ProtoForceSensorData* forcesensordata_;
  ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::RobotCommonResponse > errorinfo_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static ProtoForceSensorDataResponse* default_instance_;
};
// -------------------------------------------------------------------

class ProtoJointStatus : public ::google::protobuf::Message {
 public:
  ProtoJointStatus();
  virtual ~ProtoJointStatus();

  ProtoJointStatus(const ProtoJointStatus& from);

  inline ProtoJointStatus& operator=(const ProtoJointStatus& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoJointStatus& default_instance();

  void Swap(ProtoJointStatus* other);

  // implements Message ----------------------------------------------

  ProtoJointStatus* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoJointStatus& from);
  void MergeFrom(const ProtoJointStatus& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int32 jointCurrentI = 1;
  inline bool has_jointcurrenti() const;
  inline void clear_jointcurrenti();
  static const int kJointCurrentIFieldNumber = 1;
  inline ::google::protobuf::int32 jointcurrenti() const;
  inline void set_jointcurrenti(::google::protobuf::int32 value);

  // required int32 jointSpeedMoto = 2;
  inline bool has_jointspeedmoto() const;
  inline void clear_jointspeedmoto();
  static const int kJointSpeedMotoFieldNumber = 2;
  inline ::google::protobuf::int32 jointspeedmoto() const;
  inline void set_jointspeedmoto(::google::protobuf::int32 value);

  // required float jointPosJ = 3;
  inline bool has_jointposj() const;
  inline void clear_jointposj();
  static const int kJointPosJFieldNumber = 3;
  inline float jointposj() const;
  inline void set_jointposj(float value);

  // required float jointCurVol = 4;
  inline bool has_jointcurvol() const;
  inline void clear_jointcurvol();
  static const int kJointCurVolFieldNumber = 4;
  inline float jointcurvol() const;
  inline void set_jointcurvol(float value);

  // required float jointCurTemp = 5;
  inline bool has_jointcurtemp() const;
  inline void clear_jointcurtemp();
  static const int kJointCurTempFieldNumber = 5;
  inline float jointcurtemp() const;
  inline void set_jointcurtemp(float value);

  // required int32 jointTagCurrentI = 6;
  inline bool has_jointtagcurrenti() const;
  inline void clear_jointtagcurrenti();
  static const int kJointTagCurrentIFieldNumber = 6;
  inline ::google::protobuf::int32 jointtagcurrenti() const;
  inline void set_jointtagcurrenti(::google::protobuf::int32 value);

  // required float jointTagSpeedMoto = 7;
  inline bool has_jointtagspeedmoto() const;
  inline void clear_jointtagspeedmoto();
  static const int kJointTagSpeedMotoFieldNumber = 7;
  inline float jointtagspeedmoto() const;
  inline void set_jointtagspeedmoto(float value);

  // required float jointTagPosJ = 8;
  inline bool has_jointtagposj() const;
  inline void clear_jointtagposj();
  static const int kJointTagPosJFieldNumber = 8;
  inline float jointtagposj() const;
  inline void set_jointtagposj(float value);

  // required uint32 jointErrorNum = 9;
  inline bool has_jointerrornum() const;
  inline void clear_jointerrornum();
  static const int kJointErrorNumFieldNumber = 9;
  inline ::google::protobuf::uint32 jointerrornum() const;
  inline void set_jointerrornum(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:aubo.robot.common.ProtoJointStatus)
 private:
  inline void set_has_jointcurrenti();
  inline void clear_has_jointcurrenti();
  inline void set_has_jointspeedmoto();
  inline void clear_has_jointspeedmoto();
  inline void set_has_jointposj();
  inline void clear_has_jointposj();
  inline void set_has_jointcurvol();
  inline void clear_has_jointcurvol();
  inline void set_has_jointcurtemp();
  inline void clear_has_jointcurtemp();
  inline void set_has_jointtagcurrenti();
  inline void clear_has_jointtagcurrenti();
  inline void set_has_jointtagspeedmoto();
  inline void clear_has_jointtagspeedmoto();
  inline void set_has_jointtagposj();
  inline void clear_has_jointtagposj();
  inline void set_has_jointerrornum();
  inline void clear_has_jointerrornum();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::int32 jointcurrenti_;
  ::google::protobuf::int32 jointspeedmoto_;
  float jointposj_;
  float jointcurvol_;
  float jointcurtemp_;
  ::google::protobuf::int32 jointtagcurrenti_;
  float jointtagspeedmoto_;
  float jointtagposj_;
  ::google::protobuf::uint32 jointerrornum_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static ProtoJointStatus* default_instance_;
};
// -------------------------------------------------------------------

class ProtoRobotAllJointStatusResponse : public ::google::protobuf::Message {
 public:
  ProtoRobotAllJointStatusResponse();
  virtual ~ProtoRobotAllJointStatusResponse();

  ProtoRobotAllJointStatusResponse(const ProtoRobotAllJointStatusResponse& from);

  inline ProtoRobotAllJointStatusResponse& operator=(const ProtoRobotAllJointStatusResponse& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoRobotAllJointStatusResponse& default_instance();

  void Swap(ProtoRobotAllJointStatusResponse* other);

  // implements Message ----------------------------------------------

  ProtoRobotAllJointStatusResponse* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoRobotAllJointStatusResponse& from);
  void MergeFrom(const ProtoRobotAllJointStatusResponse& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required uint32 jointCount = 1;
  inline bool has_jointcount() const;
  inline void clear_jointcount();
  static const int kJointCountFieldNumber = 1;
  inline ::google::protobuf::uint32 jointcount() const;
  inline void set_jointcount(::google::protobuf::uint32 value);

  // repeated .aubo.robot.common.ProtoJointStatus jointStatus = 2;
  inline int jointstatus_size() const;
  inline void clear_jointstatus();
  static const int kJointStatusFieldNumber = 2;
  inline const ::aubo::robot::common::ProtoJointStatus& jointstatus(int index) const;
  inline ::aubo::robot::common::ProtoJointStatus* mutable_jointstatus(int index);
  inline ::aubo::robot::common::ProtoJointStatus* add_jointstatus();
  inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ProtoJointStatus >&
      jointstatus() const;
  inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ProtoJointStatus >*
      mutable_jointstatus();

  // repeated .aubo.robot.common.RobotCommonResponse errorInfo = 3;
  inline int errorinfo_size() const;
  inline void clear_errorinfo();
  static const int kErrorInfoFieldNumber = 3;
  inline const ::aubo::robot::common::RobotCommonResponse& errorinfo(int index) const;
  inline ::aubo::robot::common::RobotCommonResponse* mutable_errorinfo(int index);
  inline ::aubo::robot::common::RobotCommonResponse* add_errorinfo();
  inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::RobotCommonResponse >&
      errorinfo() const;
  inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::RobotCommonResponse >*
      mutable_errorinfo();

  // @@protoc_insertion_point(class_scope:aubo.robot.common.ProtoRobotAllJointStatusResponse)
 private:
  inline void set_has_jointcount();
  inline void clear_has_jointcount();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ProtoJointStatus > jointstatus_;
  ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::RobotCommonResponse > errorinfo_;
  ::google::protobuf::uint32 jointcount_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static ProtoRobotAllJointStatusResponse* default_instance_;
};
// -------------------------------------------------------------------

class relativeMove_t : public ::google::protobuf::Message {
 public:
  relativeMove_t();
  virtual ~relativeMove_t();

  relativeMove_t(const relativeMove_t& from);

  inline relativeMove_t& operator=(const relativeMove_t& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const relativeMove_t& default_instance();

  void Swap(relativeMove_t* other);

  // implements Message ----------------------------------------------

  relativeMove_t* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const relativeMove_t& from);
  void MergeFrom(const relativeMove_t& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required bool ena = 1;
  inline bool has_ena() const;
  inline void clear_ena();
  static const int kEnaFieldNumber = 1;
  inline bool ena() const;
  inline void set_ena(bool value);

  // repeated float relativePosition = 2;
  inline int relativeposition_size() const;
  inline void clear_relativeposition();
  static const int kRelativePositionFieldNumber = 2;
  inline float relativeposition(int index) const;
  inline void set_relativeposition(int index, float value);
  inline void add_relativeposition(float value);
  inline const ::google::protobuf::RepeatedField< float >&
      relativeposition() const;
  inline ::google::protobuf::RepeatedField< float >*
      mutable_relativeposition();

  // required .aubo.robot.common.cartesianOri_U relativeOrientation = 3;
  inline bool has_relativeorientation() const;
  inline void clear_relativeorientation();
  static const int kRelativeOrientationFieldNumber = 3;
  inline const ::aubo::robot::common::cartesianOri_U& relativeorientation() const;
  inline ::aubo::robot::common::cartesianOri_U* mutable_relativeorientation();
  inline ::aubo::robot::common::cartesianOri_U* release_relativeorientation();
  inline void set_allocated_relativeorientation(::aubo::robot::common::cartesianOri_U* relativeorientation);

  // @@protoc_insertion_point(class_scope:aubo.robot.common.relativeMove_t)
 private:
  inline void set_has_ena();
  inline void clear_has_ena();
  inline void set_has_relativeorientation();
  inline void clear_has_relativeorientation();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedField< float > relativeposition_;
  ::aubo::robot::common::cartesianOri_U* relativeorientation_;
  bool ena_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static relativeMove_t* default_instance_;
};
// -------------------------------------------------------------------

class joint_cart_U : public ::google::protobuf::Message {
 public:
  joint_cart_U();
  virtual ~joint_cart_U();

  joint_cart_U(const joint_cart_U& from);

  inline joint_cart_U& operator=(const joint_cart_U& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const joint_cart_U& default_instance();

  void Swap(joint_cart_U* other);

  // implements Message ----------------------------------------------

  joint_cart_U* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const joint_cart_U& from);
  void MergeFrom(const joint_cart_U& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated double cartPara = 1;
  inline int cartpara_size() const;
  inline void clear_cartpara();
  static const int kCartParaFieldNumber = 1;
  inline double cartpara(int index) const;
  inline void set_cartpara(int index, double value);
  inline void add_cartpara(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      cartpara() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_cartpara();

  // repeated double jointPara = 2;
  inline int jointpara_size() const;
  inline void clear_jointpara();
  static const int kJointParaFieldNumber = 2;
  inline double jointpara(int index) const;
  inline void set_jointpara(int index, double value);
  inline void add_jointpara(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      jointpara() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_jointpara();

  // @@protoc_insertion_point(class_scope:aubo.robot.common.joint_cart_U)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedField< double > cartpara_;
  ::google::protobuf::RepeatedField< double > jointpara_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static joint_cart_U* default_instance_;
};
// -------------------------------------------------------------------

class arrivalAhead_t : public ::google::protobuf::Message {
 public:
  arrivalAhead_t();
  virtual ~arrivalAhead_t();

  arrivalAhead_t(const arrivalAhead_t& from);

  inline arrivalAhead_t& operator=(const arrivalAhead_t& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const arrivalAhead_t& default_instance();

  void Swap(arrivalAhead_t* other);

  // implements Message ----------------------------------------------

  arrivalAhead_t* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const arrivalAhead_t& from);
  void MergeFrom(const arrivalAhead_t& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int32 arrivalAheadStat = 1;
  inline bool has_arrivalaheadstat() const;
  inline void clear_arrivalaheadstat();
  static const int kArrivalAheadStatFieldNumber = 1;
  inline ::google::protobuf::int32 arrivalaheadstat() const;
  inline void set_arrivalaheadstat(::google::protobuf::int32 value);

  // required double arrivalAheadThr = 2;
  inline bool has_arrivalaheadthr() const;
  inline void clear_arrivalaheadthr();
  static const int kArrivalAheadThrFieldNumber = 2;
  inline double arrivalaheadthr() const;
  inline void set_arrivalaheadthr(double value);

  // @@protoc_insertion_point(class_scope:aubo.robot.common.arrivalAhead_t)
 private:
  inline void set_has_arrivalaheadstat();
  inline void clear_has_arrivalaheadstat();
  inline void set_has_arrivalaheadthr();
  inline void clear_has_arrivalaheadthr();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  double arrivalaheadthr_;
  ::google::protobuf::int32 arrivalaheadstat_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static arrivalAhead_t* default_instance_;
};
// -------------------------------------------------------------------

class ProtoToolInEndDesc : public ::google::protobuf::Message {
 public:
  ProtoToolInEndDesc();
  virtual ~ProtoToolInEndDesc();

  ProtoToolInEndDesc(const ProtoToolInEndDesc& from);

  inline ProtoToolInEndDesc& operator=(const ProtoToolInEndDesc& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoToolInEndDesc& default_instance();

  void Swap(ProtoToolInEndDesc* other);

  // implements Message ----------------------------------------------

  ProtoToolInEndDesc* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoToolInEndDesc& from);
  void MergeFrom(const ProtoToolInEndDesc& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .aubo.robot.common.Pos toolInEndPosition = 1;
  inline bool has_toolinendposition() const;
  inline void clear_toolinendposition();
  static const int kToolInEndPositionFieldNumber = 1;
  inline const ::aubo::robot::common::Pos& toolinendposition() const;
  inline ::aubo::robot::common::Pos* mutable_toolinendposition();
  inline ::aubo::robot::common::Pos* release_toolinendposition();
  inline void set_allocated_toolinendposition(::aubo::robot::common::Pos* toolinendposition);

  // required .aubo.robot.common.Ori toolInEndOrientation = 2;
  inline bool has_toolinendorientation() const;
  inline void clear_toolinendorientation();
  static const int kToolInEndOrientationFieldNumber = 2;
  inline const ::aubo::robot::common::Ori& toolinendorientation() const;
  inline ::aubo::robot::common::Ori* mutable_toolinendorientation();
  inline ::aubo::robot::common::Ori* release_toolinendorientation();
  inline void set_allocated_toolinendorientation(::aubo::robot::common::Ori* toolinendorientation);

  // @@protoc_insertion_point(class_scope:aubo.robot.common.ProtoToolInEndDesc)
 private:
  inline void set_has_toolinendposition();
  inline void clear_has_toolinendposition();
  inline void set_has_toolinendorientation();
  inline void clear_has_toolinendorientation();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::aubo::robot::common::Pos* toolinendposition_;
  ::aubo::robot::common::Ori* toolinendorientation_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static ProtoToolInEndDesc* default_instance_;
};
// -------------------------------------------------------------------

class RobotMoveProfile : public ::google::protobuf::Message {
 public:
  RobotMoveProfile();
  virtual ~RobotMoveProfile();

  RobotMoveProfile(const RobotMoveProfile& from);

  inline RobotMoveProfile& operator=(const RobotMoveProfile& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RobotMoveProfile& default_instance();

  void Swap(RobotMoveProfile* other);

  // implements Message ----------------------------------------------

  RobotMoveProfile* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RobotMoveProfile& from);
  void MergeFrom(const RobotMoveProfile& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .aubo.robot.movecondition.move_mode moveMode = 1;
  inline bool has_movemode() const;
  inline void clear_movemode();
  static const int kMoveModeFieldNumber = 1;
  inline ::aubo::robot::movecondition::move_mode movemode() const;
  inline void set_movemode(::aubo::robot::movecondition::move_mode value);

  // required .aubo.robot.movecondition.move_track subMoveMode = 2;
  inline bool has_submovemode() const;
  inline void clear_submovemode();
  static const int kSubMoveModeFieldNumber = 2;
  inline ::aubo::robot::movecondition::move_track submovemode() const;
  inline void set_submovemode(::aubo::robot::movecondition::move_track value);

  // required .aubo.robot.movecondition.teach_mode teachMode = 3;
  inline bool has_teachmode() const;
  inline void clear_teachmode();
  static const int kTeachModeFieldNumber = 3;
  inline ::aubo::robot::movecondition::teach_mode teachmode() const;
  inline void set_teachmode(::aubo::robot::movecondition::teach_mode value);

  // required bool enableIterIk = 4;
  inline bool has_enableiterik() const;
  inline void clear_enableiterik();
  static const int kEnableIterIkFieldNumber = 4;
  inline bool enableiterik() const;
  inline void set_enableiterik(bool value);

  // required bool toolTrack = 5;
  inline bool has_tooltrack() const;
  inline void clear_tooltrack();
  static const int kToolTrackFieldNumber = 5;
  inline bool tooltrack() const;
  inline void set_tooltrack(bool value);

  // required .aubo.robot.common.Pos toolInEndPosition = 6;
  inline bool has_toolinendposition() const;
  inline void clear_toolinendposition();
  static const int kToolInEndPositionFieldNumber = 6;
  inline const ::aubo::robot::common::Pos& toolinendposition() const;
  inline ::aubo::robot::common::Pos* mutable_toolinendposition();
  inline ::aubo::robot::common::Pos* release_toolinendposition();
  inline void set_allocated_toolinendposition(::aubo::robot::common::Pos* toolinendposition);

  // required .aubo.robot.common.Ori toolInEndOrientation = 7;
  inline bool has_toolinendorientation() const;
  inline void clear_toolinendorientation();
  static const int kToolInEndOrientationFieldNumber = 7;
  inline const ::aubo::robot::common::Ori& toolinendorientation() const;
  inline ::aubo::robot::common::Ori* mutable_toolinendorientation();
  inline ::aubo::robot::common::Ori* release_toolinendorientation();
  inline void set_allocated_toolinendorientation(::aubo::robot::common::Ori* toolinendorientation);

  // required .aubo.robot.common.relativeMove_t relativeMove = 8;
  inline bool has_relativemove() const;
  inline void clear_relativemove();
  static const int kRelativeMoveFieldNumber = 8;
  inline const ::aubo::robot::common::relativeMove_t& relativemove() const;
  inline ::aubo::robot::common::relativeMove_t* mutable_relativemove();
  inline ::aubo::robot::common::relativeMove_t* release_relativemove();
  inline void set_allocated_relativemove(::aubo::robot::common::relativeMove_t* relativemove);

  // required .aubo.robot.common.joint_cart_U maxVelc = 9;
  inline bool has_maxvelc() const;
  inline void clear_maxvelc();
  static const int kMaxVelcFieldNumber = 9;
  inline const ::aubo::robot::common::joint_cart_U& maxvelc() const;
  inline ::aubo::robot::common::joint_cart_U* mutable_maxvelc();
  inline ::aubo::robot::common::joint_cart_U* release_maxvelc();
  inline void set_allocated_maxvelc(::aubo::robot::common::joint_cart_U* maxvelc);

  // required .aubo.robot.common.joint_cart_U maxAcc = 10;
  inline bool has_maxacc() const;
  inline void clear_maxacc();
  static const int kMaxAccFieldNumber = 10;
  inline const ::aubo::robot::common::joint_cart_U& maxacc() const;
  inline ::aubo::robot::common::joint_cart_U* mutable_maxacc();
  inline ::aubo::robot::common::joint_cart_U* release_maxacc();
  inline void set_allocated_maxacc(::aubo::robot::common::joint_cart_U* maxacc);

  // required float blendRadius = 11;
  inline bool has_blendradius() const;
  inline void clear_blendradius();
  static const int kBlendRadiusFieldNumber = 11;
  inline float blendradius() const;
  inline void set_blendradius(float value);

  // required int32 circularLoopTimes = 12;
  inline bool has_circularlooptimes() const;
  inline void clear_circularlooptimes();
  static const int kCircularLoopTimesFieldNumber = 12;
  inline ::google::protobuf::int32 circularlooptimes() const;
  inline void set_circularlooptimes(::google::protobuf::int32 value);

  // required .aubo.robot.common.arrivalAhead_t arrivalAhead = 13;
  inline bool has_arrivalahead() const;
  inline void clear_arrivalahead();
  static const int kArrivalAheadFieldNumber = 13;
  inline const ::aubo::robot::common::arrivalAhead_t& arrivalahead() const;
  inline ::aubo::robot::common::arrivalAhead_t* mutable_arrivalahead();
  inline ::aubo::robot::common::arrivalAhead_t* release_arrivalahead();
  inline void set_allocated_arrivalahead(::aubo::robot::common::arrivalAhead_t* arrivalahead);

  // @@protoc_insertion_point(class_scope:aubo.robot.common.RobotMoveProfile)
 private:
  inline void set_has_movemode();
  inline void clear_has_movemode();
  inline void set_has_submovemode();
  inline void clear_has_submovemode();
  inline void set_has_teachmode();
  inline void clear_has_teachmode();
  inline void set_has_enableiterik();
  inline void clear_has_enableiterik();
  inline void set_has_tooltrack();
  inline void clear_has_tooltrack();
  inline void set_has_toolinendposition();
  inline void clear_has_toolinendposition();
  inline void set_has_toolinendorientation();
  inline void clear_has_toolinendorientation();
  inline void set_has_relativemove();
  inline void clear_has_relativemove();
  inline void set_has_maxvelc();
  inline void clear_has_maxvelc();
  inline void set_has_maxacc();
  inline void clear_has_maxacc();
  inline void set_has_blendradius();
  inline void clear_has_blendradius();
  inline void set_has_circularlooptimes();
  inline void clear_has_circularlooptimes();
  inline void set_has_arrivalahead();
  inline void clear_has_arrivalahead();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  int movemode_;
  int submovemode_;
  int teachmode_;
  bool enableiterik_;
  bool tooltrack_;
  ::aubo::robot::common::Pos* toolinendposition_;
  ::aubo::robot::common::Ori* toolinendorientation_;
  ::aubo::robot::common::relativeMove_t* relativemove_;
  ::aubo::robot::common::joint_cart_U* maxvelc_;
  ::aubo::robot::common::joint_cart_U* maxacc_;
  float blendradius_;
  ::google::protobuf::int32 circularlooptimes_;
  ::aubo::robot::common::arrivalAhead_t* arrivalahead_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static RobotMoveProfile* default_instance_;
};
// -------------------------------------------------------------------

class RobotMove : public ::google::protobuf::Message {
 public:
  RobotMove();
  virtual ~RobotMove();

  RobotMove(const RobotMove& from);

  inline RobotMove& operator=(const RobotMove& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RobotMove& default_instance();

  void Swap(RobotMove* other);

  // implements Message ----------------------------------------------

  RobotMove* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RobotMove& from);
  void MergeFrom(const RobotMove& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .aubo.robot.common.RobotMoveProfile move_profile = 1;
  inline bool has_move_profile() const;
  inline void clear_move_profile();
  static const int kMoveProfileFieldNumber = 1;
  inline const ::aubo::robot::common::RobotMoveProfile& move_profile() const;
  inline ::aubo::robot::common::RobotMoveProfile* mutable_move_profile();
  inline ::aubo::robot::common::RobotMoveProfile* release_move_profile();
  inline void set_allocated_move_profile(::aubo::robot::common::RobotMoveProfile* move_profile);

  // repeated .aubo.robot.common.ProtoRoadPoint roadPointVector = 2;
  inline int roadpointvector_size() const;
  inline void clear_roadpointvector();
  static const int kRoadPointVectorFieldNumber = 2;
  inline const ::aubo::robot::common::ProtoRoadPoint& roadpointvector(int index) const;
  inline ::aubo::robot::common::ProtoRoadPoint* mutable_roadpointvector(int index);
  inline ::aubo::robot::common::ProtoRoadPoint* add_roadpointvector();
  inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ProtoRoadPoint >&
      roadpointvector() const;
  inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ProtoRoadPoint >*
      mutable_roadpointvector();

  // @@protoc_insertion_point(class_scope:aubo.robot.common.RobotMove)
 private:
  inline void set_has_move_profile();
  inline void clear_has_move_profile();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::aubo::robot::common::RobotMoveProfile* move_profile_;
  ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ProtoRoadPoint > roadpointvector_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static RobotMove* default_instance_;
};
// -------------------------------------------------------------------

class RobotTeachMove : public ::google::protobuf::Message {
 public:
  RobotTeachMove();
  virtual ~RobotTeachMove();

  RobotTeachMove(const RobotTeachMove& from);

  inline RobotTeachMove& operator=(const RobotTeachMove& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RobotTeachMove& default_instance();

  void Swap(RobotTeachMove* other);

  // implements Message ----------------------------------------------

  RobotTeachMove* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RobotTeachMove& from);
  void MergeFrom(const RobotTeachMove& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .aubo.robot.common.RobotMoveProfile move_profile = 1;
  inline bool has_move_profile() const;
  inline void clear_move_profile();
  static const int kMoveProfileFieldNumber = 1;
  inline const ::aubo::robot::common::RobotMoveProfile& move_profile() const;
  inline ::aubo::robot::common::RobotMoveProfile* mutable_move_profile();
  inline ::aubo::robot::common::RobotMoveProfile* release_move_profile();
  inline void set_allocated_move_profile(::aubo::robot::common::RobotMoveProfile* move_profile);

  // required int32 coordinateSystemType = 2;
  inline bool has_coordinatesystemtype() const;
  inline void clear_coordinatesystemtype();
  static const int kCoordinateSystemTypeFieldNumber = 2;
  inline ::google::protobuf::int32 coordinatesystemtype() const;
  inline void set_coordinatesystemtype(::google::protobuf::int32 value);

  // repeated .aubo.robot.common.ProtoRoadPoint calibrateRoadPointVector = 3;
  inline int calibrateroadpointvector_size() const;
  inline void clear_calibrateroadpointvector();
  static const int kCalibrateRoadPointVectorFieldNumber = 3;
  inline const ::aubo::robot::common::ProtoRoadPoint& calibrateroadpointvector(int index) const;
  inline ::aubo::robot::common::ProtoRoadPoint* mutable_calibrateroadpointvector(int index);
  inline ::aubo::robot::common::ProtoRoadPoint* add_calibrateroadpointvector();
  inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ProtoRoadPoint >&
      calibrateroadpointvector() const;
  inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ProtoRoadPoint >*
      mutable_calibrateroadpointvector();

  // repeated int32 calibrateMathod = 4;
  inline int calibratemathod_size() const;
  inline void clear_calibratemathod();
  static const int kCalibrateMathodFieldNumber = 4;
  inline ::google::protobuf::int32 calibratemathod(int index) const;
  inline void set_calibratemathod(int index, ::google::protobuf::int32 value);
  inline void add_calibratemathod(::google::protobuf::int32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      calibratemathod() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_calibratemathod();

  // repeated .aubo.robot.common.ProtoToolInEndDesc toolInEndDesc = 5;
  inline int toolinenddesc_size() const;
  inline void clear_toolinenddesc();
  static const int kToolInEndDescFieldNumber = 5;
  inline const ::aubo::robot::common::ProtoToolInEndDesc& toolinenddesc(int index) const;
  inline ::aubo::robot::common::ProtoToolInEndDesc* mutable_toolinenddesc(int index);
  inline ::aubo::robot::common::ProtoToolInEndDesc* add_toolinenddesc();
  inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ProtoToolInEndDesc >&
      toolinenddesc() const;
  inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ProtoToolInEndDesc >*
      mutable_toolinenddesc();

  // @@protoc_insertion_point(class_scope:aubo.robot.common.RobotTeachMove)
 private:
  inline void set_has_move_profile();
  inline void clear_has_move_profile();
  inline void set_has_coordinatesystemtype();
  inline void clear_has_coordinatesystemtype();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::aubo::robot::common::RobotMoveProfile* move_profile_;
  ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ProtoRoadPoint > calibrateroadpointvector_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > calibratemathod_;
  ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ProtoToolInEndDesc > toolinenddesc_;
  ::google::protobuf::int32 coordinatesystemtype_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static RobotTeachMove* default_instance_;
};
// -------------------------------------------------------------------

class RobotCommonResponse : public ::google::protobuf::Message {
 public:
  RobotCommonResponse();
  virtual ~RobotCommonResponse();

  RobotCommonResponse(const RobotCommonResponse& from);

  inline RobotCommonResponse& operator=(const RobotCommonResponse& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RobotCommonResponse& default_instance();

  void Swap(RobotCommonResponse* other);

  // implements Message ----------------------------------------------

  RobotCommonResponse* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RobotCommonResponse& from);
  void MergeFrom(const RobotCommonResponse& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int32 errorCode = 1;
  inline bool has_errorcode() const;
  inline void clear_errorcode();
  static const int kErrorCodeFieldNumber = 1;
  inline ::google::protobuf::int32 errorcode() const;
  inline void set_errorcode(::google::protobuf::int32 value);

  // required string errorMsg = 2;
  inline bool has_errormsg() const;
  inline void clear_errormsg();
  static const int kErrorMsgFieldNumber = 2;
  inline const ::std::string& errormsg() const;
  inline void set_errormsg(const ::std::string& value);
  inline void set_errormsg(const char* value);
  inline void set_errormsg(const char* value, size_t size);
  inline ::std::string* mutable_errormsg();
  inline ::std::string* release_errormsg();
  inline void set_allocated_errormsg(::std::string* errormsg);

  // @@protoc_insertion_point(class_scope:aubo.robot.common.RobotCommonResponse)
 private:
  inline void set_has_errorcode();
  inline void clear_has_errorcode();
  inline void set_has_errormsg();
  inline void clear_has_errormsg();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::std::string* errormsg_;
  ::google::protobuf::int32 errorcode_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static RobotCommonResponse* default_instance_;
};
// -------------------------------------------------------------------

class ProtoResponseRobotTcpParam : public ::google::protobuf::Message {
 public:
  ProtoResponseRobotTcpParam();
  virtual ~ProtoResponseRobotTcpParam();

  ProtoResponseRobotTcpParam(const ProtoResponseRobotTcpParam& from);

  inline ProtoResponseRobotTcpParam& operator=(const ProtoResponseRobotTcpParam& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoResponseRobotTcpParam& default_instance();

  void Swap(ProtoResponseRobotTcpParam* other);

  // implements Message ----------------------------------------------

  ProtoResponseRobotTcpParam* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoResponseRobotTcpParam& from);
  void MergeFrom(const ProtoResponseRobotTcpParam& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .aubo.robot.communication.RobotTcpParam tcpParam = 1;
  inline int tcpparam_size() const;
  inline void clear_tcpparam();
  static const int kTcpParamFieldNumber = 1;
  inline const ::aubo::robot::communication::RobotTcpParam& tcpparam(int index) const;
  inline ::aubo::robot::communication::RobotTcpParam* mutable_tcpparam(int index);
  inline ::aubo::robot::communication::RobotTcpParam* add_tcpparam();
  inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::RobotTcpParam >&
      tcpparam() const;
  inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::RobotTcpParam >*
      mutable_tcpparam();

  // required .aubo.robot.common.RobotCommonResponse errorInfo = 2;
  inline bool has_errorinfo() const;
  inline void clear_errorinfo();
  static const int kErrorInfoFieldNumber = 2;
  inline const ::aubo::robot::common::RobotCommonResponse& errorinfo() const;
  inline ::aubo::robot::common::RobotCommonResponse* mutable_errorinfo();
  inline ::aubo::robot::common::RobotCommonResponse* release_errorinfo();
  inline void set_allocated_errorinfo(::aubo::robot::common::RobotCommonResponse* errorinfo);

  // @@protoc_insertion_point(class_scope:aubo.robot.common.ProtoResponseRobotTcpParam)
 private:
  inline void set_has_errorinfo();
  inline void clear_has_errorinfo();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::RobotTcpParam > tcpparam_;
  ::aubo::robot::common::RobotCommonResponse* errorinfo_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static ProtoResponseRobotTcpParam* default_instance_;
};
// -------------------------------------------------------------------

class ProtoResponseRobotGravityComponent : public ::google::protobuf::Message {
 public:
  ProtoResponseRobotGravityComponent();
  virtual ~ProtoResponseRobotGravityComponent();

  ProtoResponseRobotGravityComponent(const ProtoResponseRobotGravityComponent& from);

  inline ProtoResponseRobotGravityComponent& operator=(const ProtoResponseRobotGravityComponent& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoResponseRobotGravityComponent& default_instance();

  void Swap(ProtoResponseRobotGravityComponent* other);

  // implements Message ----------------------------------------------

  ProtoResponseRobotGravityComponent* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoResponseRobotGravityComponent& from);
  void MergeFrom(const ProtoResponseRobotGravityComponent& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .aubo.robot.communication.RobotGravityComponent gravityComponent = 1;
  inline int gravitycomponent_size() const;
  inline void clear_gravitycomponent();
  static const int kGravityComponentFieldNumber = 1;
  inline const ::aubo::robot::communication::RobotGravityComponent& gravitycomponent(int index) const;
  inline ::aubo::robot::communication::RobotGravityComponent* mutable_gravitycomponent(int index);
  inline ::aubo::robot::communication::RobotGravityComponent* add_gravitycomponent();
  inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::RobotGravityComponent >&
      gravitycomponent() const;
  inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::RobotGravityComponent >*
      mutable_gravitycomponent();

  // required .aubo.robot.common.RobotCommonResponse errorInfo = 2;
  inline bool has_errorinfo() const;
  inline void clear_errorinfo();
  static const int kErrorInfoFieldNumber = 2;
  inline const ::aubo::robot::common::RobotCommonResponse& errorinfo() const;
  inline ::aubo::robot::common::RobotCommonResponse* mutable_errorinfo();
  inline ::aubo::robot::common::RobotCommonResponse* release_errorinfo();
  inline void set_allocated_errorinfo(::aubo::robot::common::RobotCommonResponse* errorinfo);

  // @@protoc_insertion_point(class_scope:aubo.robot.common.ProtoResponseRobotGravityComponent)
 private:
  inline void set_has_errorinfo();
  inline void clear_has_errorinfo();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::RobotGravityComponent > gravitycomponent_;
  ::aubo::robot::common::RobotCommonResponse* errorinfo_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static ProtoResponseRobotGravityComponent* default_instance_;
};
// -------------------------------------------------------------------

class ProtoResponseRobotCollisionCurrent : public ::google::protobuf::Message {
 public:
  ProtoResponseRobotCollisionCurrent();
  virtual ~ProtoResponseRobotCollisionCurrent();

  ProtoResponseRobotCollisionCurrent(const ProtoResponseRobotCollisionCurrent& from);

  inline ProtoResponseRobotCollisionCurrent& operator=(const ProtoResponseRobotCollisionCurrent& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoResponseRobotCollisionCurrent& default_instance();

  void Swap(ProtoResponseRobotCollisionCurrent* other);

  // implements Message ----------------------------------------------

  ProtoResponseRobotCollisionCurrent* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoResponseRobotCollisionCurrent& from);
  void MergeFrom(const ProtoResponseRobotCollisionCurrent& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .aubo.robot.communication.RobotCollisionCurrent collisionCurrent = 1;
  inline int collisioncurrent_size() const;
  inline void clear_collisioncurrent();
  static const int kCollisionCurrentFieldNumber = 1;
  inline const ::aubo::robot::communication::RobotCollisionCurrent& collisioncurrent(int index) const;
  inline ::aubo::robot::communication::RobotCollisionCurrent* mutable_collisioncurrent(int index);
  inline ::aubo::robot::communication::RobotCollisionCurrent* add_collisioncurrent();
  inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::RobotCollisionCurrent >&
      collisioncurrent() const;
  inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::RobotCollisionCurrent >*
      mutable_collisioncurrent();

  // required .aubo.robot.common.RobotCommonResponse errorInfo = 2;
  inline bool has_errorinfo() const;
  inline void clear_errorinfo();
  static const int kErrorInfoFieldNumber = 2;
  inline const ::aubo::robot::common::RobotCommonResponse& errorinfo() const;
  inline ::aubo::robot::common::RobotCommonResponse* mutable_errorinfo();
  inline ::aubo::robot::common::RobotCommonResponse* release_errorinfo();
  inline void set_allocated_errorinfo(::aubo::robot::common::RobotCommonResponse* errorinfo);

  // @@protoc_insertion_point(class_scope:aubo.robot.common.ProtoResponseRobotCollisionCurrent)
 private:
  inline void set_has_errorinfo();
  inline void clear_has_errorinfo();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::RobotCollisionCurrent > collisioncurrent_;
  ::aubo::robot::common::RobotCommonResponse* errorinfo_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static ProtoResponseRobotCollisionCurrent* default_instance_;
};
// -------------------------------------------------------------------

class ProtoResponseRobotDevInfo : public ::google::protobuf::Message {
 public:
  ProtoResponseRobotDevInfo();
  virtual ~ProtoResponseRobotDevInfo();

  ProtoResponseRobotDevInfo(const ProtoResponseRobotDevInfo& from);

  inline ProtoResponseRobotDevInfo& operator=(const ProtoResponseRobotDevInfo& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoResponseRobotDevInfo& default_instance();

  void Swap(ProtoResponseRobotDevInfo* other);

  // implements Message ----------------------------------------------

  ProtoResponseRobotDevInfo* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoResponseRobotDevInfo& from);
  void MergeFrom(const ProtoResponseRobotDevInfo& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .aubo.robot.communication.OurRobotDevInfo devInfo = 1;
  inline int devinfo_size() const;
  inline void clear_devinfo();
  static const int kDevInfoFieldNumber = 1;
  inline const ::aubo::robot::communication::OurRobotDevInfo& devinfo(int index) const;
  inline ::aubo::robot::communication::OurRobotDevInfo* mutable_devinfo(int index);
  inline ::aubo::robot::communication::OurRobotDevInfo* add_devinfo();
  inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::OurRobotDevInfo >&
      devinfo() const;
  inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::OurRobotDevInfo >*
      mutable_devinfo();

  // required .aubo.robot.common.RobotCommonResponse errorInfo = 2;
  inline bool has_errorinfo() const;
  inline void clear_errorinfo();
  static const int kErrorInfoFieldNumber = 2;
  inline const ::aubo::robot::common::RobotCommonResponse& errorinfo() const;
  inline ::aubo::robot::common::RobotCommonResponse* mutable_errorinfo();
  inline ::aubo::robot::common::RobotCommonResponse* release_errorinfo();
  inline void set_allocated_errorinfo(::aubo::robot::common::RobotCommonResponse* errorinfo);

  // @@protoc_insertion_point(class_scope:aubo.robot.common.ProtoResponseRobotDevInfo)
 private:
  inline void set_has_errorinfo();
  inline void clear_has_errorinfo();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::OurRobotDevInfo > devinfo_;
  ::aubo::robot::common::RobotCommonResponse* errorinfo_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static ProtoResponseRobotDevInfo* default_instance_;
};
// -------------------------------------------------------------------

class ToolInertia : public ::google::protobuf::Message {
 public:
  ToolInertia();
  virtual ~ToolInertia();

  ToolInertia(const ToolInertia& from);

  inline ToolInertia& operator=(const ToolInertia& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ToolInertia& default_instance();

  void Swap(ToolInertia* other);

  // implements Message ----------------------------------------------

  ToolInertia* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ToolInertia& from);
  void MergeFrom(const ToolInertia& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required double xx = 1;
  inline bool has_xx() const;
  inline void clear_xx();
  static const int kXxFieldNumber = 1;
  inline double xx() const;
  inline void set_xx(double value);

  // required double xy = 2;
  inline bool has_xy() const;
  inline void clear_xy();
  static const int kXyFieldNumber = 2;
  inline double xy() const;
  inline void set_xy(double value);

  // required double xz = 3;
  inline bool has_xz() const;
  inline void clear_xz();
  static const int kXzFieldNumber = 3;
  inline double xz() const;
  inline void set_xz(double value);

  // required double yy = 4;
  inline bool has_yy() const;
  inline void clear_yy();
  static const int kYyFieldNumber = 4;
  inline double yy() const;
  inline void set_yy(double value);

  // required double yz = 5;
  inline bool has_yz() const;
  inline void clear_yz();
  static const int kYzFieldNumber = 5;
  inline double yz() const;
  inline void set_yz(double value);

  // required double zz = 6;
  inline bool has_zz() const;
  inline void clear_zz();
  static const int kZzFieldNumber = 6;
  inline double zz() const;
  inline void set_zz(double value);

  // @@protoc_insertion_point(class_scope:aubo.robot.common.ToolInertia)
 private:
  inline void set_has_xx();
  inline void clear_has_xx();
  inline void set_has_xy();
  inline void clear_has_xy();
  inline void set_has_xz();
  inline void clear_has_xz();
  inline void set_has_yy();
  inline void clear_has_yy();
  inline void set_has_yz();
  inline void clear_has_yz();
  inline void set_has_zz();
  inline void clear_has_zz();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  double xx_;
  double xy_;
  double xz_;
  double yy_;
  double yz_;
  double zz_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static ToolInertia* default_instance_;
};
// -------------------------------------------------------------------

class ToolDynamicsParam : public ::google::protobuf::Message {
 public:
  ToolDynamicsParam();
  virtual ~ToolDynamicsParam();

  ToolDynamicsParam(const ToolDynamicsParam& from);

  inline ToolDynamicsParam& operator=(const ToolDynamicsParam& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ToolDynamicsParam& default_instance();

  void Swap(ToolDynamicsParam* other);

  // implements Message ----------------------------------------------

  ToolDynamicsParam* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ToolDynamicsParam& from);
  void MergeFrom(const ToolDynamicsParam& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required double positionX = 1;
  inline bool has_positionx() const;
  inline void clear_positionx();
  static const int kPositionXFieldNumber = 1;
  inline double positionx() const;
  inline void set_positionx(double value);

  // required double positionY = 2;
  inline bool has_positiony() const;
  inline void clear_positiony();
  static const int kPositionYFieldNumber = 2;
  inline double positiony() const;
  inline void set_positiony(double value);

  // required double positionZ = 3;
  inline bool has_positionz() const;
  inline void clear_positionz();
  static const int kPositionZFieldNumber = 3;
  inline double positionz() const;
  inline void set_positionz(double value);

  // required double payload = 4;
  inline bool has_payload() const;
  inline void clear_payload();
  static const int kPayloadFieldNumber = 4;
  inline double payload() const;
  inline void set_payload(double value);

  // required .aubo.robot.common.ToolInertia toolInertia = 5;
  inline bool has_toolinertia() const;
  inline void clear_toolinertia();
  static const int kToolInertiaFieldNumber = 5;
  inline const ::aubo::robot::common::ToolInertia& toolinertia() const;
  inline ::aubo::robot::common::ToolInertia* mutable_toolinertia();
  inline ::aubo::robot::common::ToolInertia* release_toolinertia();
  inline void set_allocated_toolinertia(::aubo::robot::common::ToolInertia* toolinertia);

  // @@protoc_insertion_point(class_scope:aubo.robot.common.ToolDynamicsParam)
 private:
  inline void set_has_positionx();
  inline void clear_has_positionx();
  inline void set_has_positiony();
  inline void clear_has_positiony();
  inline void set_has_positionz();
  inline void clear_has_positionz();
  inline void set_has_payload();
  inline void clear_has_payload();
  inline void set_has_toolinertia();
  inline void clear_has_toolinertia();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  double positionx_;
  double positiony_;
  double positionz_;
  double payload_;
  ::aubo::robot::common::ToolInertia* toolinertia_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static ToolDynamicsParam* default_instance_;
};
// -------------------------------------------------------------------

class ToolKinematicsParam : public ::google::protobuf::Message {
 public:
  ToolKinematicsParam();
  virtual ~ToolKinematicsParam();

  ToolKinematicsParam(const ToolKinematicsParam& from);

  inline ToolKinematicsParam& operator=(const ToolKinematicsParam& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ToolKinematicsParam& default_instance();

  void Swap(ToolKinematicsParam* other);

  // implements Message ----------------------------------------------

  ToolKinematicsParam* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ToolKinematicsParam& from);
  void MergeFrom(const ToolKinematicsParam& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .aubo.robot.common.Pos toolPosition = 1;
  inline bool has_toolposition() const;
  inline void clear_toolposition();
  static const int kToolPositionFieldNumber = 1;
  inline const ::aubo::robot::common::Pos& toolposition() const;
  inline ::aubo::robot::common::Pos* mutable_toolposition();
  inline ::aubo::robot::common::Pos* release_toolposition();
  inline void set_allocated_toolposition(::aubo::robot::common::Pos* toolposition);

  // required .aubo.robot.common.Ori toolOri = 2;
  inline bool has_toolori() const;
  inline void clear_toolori();
  static const int kToolOriFieldNumber = 2;
  inline const ::aubo::robot::common::Ori& toolori() const;
  inline ::aubo::robot::common::Ori* mutable_toolori();
  inline ::aubo::robot::common::Ori* release_toolori();
  inline void set_allocated_toolori(::aubo::robot::common::Ori* toolori);

  // @@protoc_insertion_point(class_scope:aubo.robot.common.ToolKinematicsParam)
 private:
  inline void set_has_toolposition();
  inline void clear_has_toolposition();
  inline void set_has_toolori();
  inline void clear_has_toolori();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::aubo::robot::common::Pos* toolposition_;
  ::aubo::robot::common::Ori* toolori_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static ToolKinematicsParam* default_instance_;
};
// -------------------------------------------------------------------

class ToolParam : public ::google::protobuf::Message {
 public:
  ToolParam();
  virtual ~ToolParam();

  ToolParam(const ToolParam& from);

  inline ToolParam& operator=(const ToolParam& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ToolParam& default_instance();

  void Swap(ToolParam* other);

  // implements Message ----------------------------------------------

  ToolParam* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ToolParam& from);
  void MergeFrom(const ToolParam& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .aubo.robot.common.ToolDynamicsParam toolDynamicsParam = 1;
  inline int tooldynamicsparam_size() const;
  inline void clear_tooldynamicsparam();
  static const int kToolDynamicsParamFieldNumber = 1;
  inline const ::aubo::robot::common::ToolDynamicsParam& tooldynamicsparam(int index) const;
  inline ::aubo::robot::common::ToolDynamicsParam* mutable_tooldynamicsparam(int index);
  inline ::aubo::robot::common::ToolDynamicsParam* add_tooldynamicsparam();
  inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ToolDynamicsParam >&
      tooldynamicsparam() const;
  inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ToolDynamicsParam >*
      mutable_tooldynamicsparam();

  // repeated .aubo.robot.common.ToolKinematicsParam toolKinematicsParam = 2;
  inline int toolkinematicsparam_size() const;
  inline void clear_toolkinematicsparam();
  static const int kToolKinematicsParamFieldNumber = 2;
  inline const ::aubo::robot::common::ToolKinematicsParam& toolkinematicsparam(int index) const;
  inline ::aubo::robot::common::ToolKinematicsParam* mutable_toolkinematicsparam(int index);
  inline ::aubo::robot::common::ToolKinematicsParam* add_toolkinematicsparam();
  inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ToolKinematicsParam >&
      toolkinematicsparam() const;
  inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ToolKinematicsParam >*
      mutable_toolkinematicsparam();

  // required .aubo.robot.common.RobotCommonResponse errorInfo = 3;
  inline bool has_errorinfo() const;
  inline void clear_errorinfo();
  static const int kErrorInfoFieldNumber = 3;
  inline const ::aubo::robot::common::RobotCommonResponse& errorinfo() const;
  inline ::aubo::robot::common::RobotCommonResponse* mutable_errorinfo();
  inline ::aubo::robot::common::RobotCommonResponse* release_errorinfo();
  inline void set_allocated_errorinfo(::aubo::robot::common::RobotCommonResponse* errorinfo);

  // @@protoc_insertion_point(class_scope:aubo.robot.common.ToolParam)
 private:
  inline void set_has_errorinfo();
  inline void clear_has_errorinfo();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ToolDynamicsParam > tooldynamicsparam_;
  ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ToolKinematicsParam > toolkinematicsparam_;
  ::aubo::robot::common::RobotCommonResponse* errorinfo_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static ToolParam* default_instance_;
};
// -------------------------------------------------------------------

class ProtoConveyorTrackValuePoint : public ::google::protobuf::Message {
 public:
  ProtoConveyorTrackValuePoint();
  virtual ~ProtoConveyorTrackValuePoint();

  ProtoConveyorTrackValuePoint(const ProtoConveyorTrackValuePoint& from);

  inline ProtoConveyorTrackValuePoint& operator=(const ProtoConveyorTrackValuePoint& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoConveyorTrackValuePoint& default_instance();

  void Swap(ProtoConveyorTrackValuePoint* other);

  // implements Message ----------------------------------------------

  ProtoConveyorTrackValuePoint* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoConveyorTrackValuePoint& from);
  void MergeFrom(const ProtoConveyorTrackValuePoint& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .aubo.robot.common.Pos position = 1;
  inline bool has_position() const;
  inline void clear_position();
  static const int kPositionFieldNumber = 1;
  inline const ::aubo::robot::common::Pos& position() const;
  inline ::aubo::robot::common::Pos* mutable_position();
  inline ::aubo::robot::common::Pos* release_position();
  inline void set_allocated_position(::aubo::robot::common::Pos* position);

  // required .aubo.robot.common.Ori ori = 2;
  inline bool has_ori() const;
  inline void clear_ori();
  static const int kOriFieldNumber = 2;
  inline const ::aubo::robot::common::Ori& ori() const;
  inline ::aubo::robot::common::Ori* mutable_ori();
  inline ::aubo::robot::common::Ori* release_ori();
  inline void set_allocated_ori(::aubo::robot::common::Ori* ori);

  // required int32 timestamp = 3;
  inline bool has_timestamp() const;
  inline void clear_timestamp();
  static const int kTimestampFieldNumber = 3;
  inline ::google::protobuf::int32 timestamp() const;
  inline void set_timestamp(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:aubo.robot.common.ProtoConveyorTrackValuePoint)
 private:
  inline void set_has_position();
  inline void clear_has_position();
  inline void set_has_ori();
  inline void clear_has_ori();
  inline void set_has_timestamp();
  inline void clear_has_timestamp();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::aubo::robot::common::Pos* position_;
  ::aubo::robot::common::Ori* ori_;
  ::google::protobuf::int32 timestamp_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static ProtoConveyorTrackValuePoint* default_instance_;
};
// -------------------------------------------------------------------

class ProtoRobotSafetyConfig : public ::google::protobuf::Message {
 public:
  ProtoRobotSafetyConfig();
  virtual ~ProtoRobotSafetyConfig();

  ProtoRobotSafetyConfig(const ProtoRobotSafetyConfig& from);

  inline ProtoRobotSafetyConfig& operator=(const ProtoRobotSafetyConfig& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoRobotSafetyConfig& default_instance();

  void Swap(ProtoRobotSafetyConfig* other);

  // implements Message ----------------------------------------------

  ProtoRobotSafetyConfig* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoRobotSafetyConfig& from);
  void MergeFrom(const ProtoRobotSafetyConfig& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated int32 robotReducedConfigJointSpeed = 1;
  inline int robotreducedconfigjointspeed_size() const;
  inline void clear_robotreducedconfigjointspeed();
  static const int kRobotReducedConfigJointSpeedFieldNumber = 1;
  inline ::google::protobuf::int32 robotreducedconfigjointspeed(int index) const;
  inline void set_robotreducedconfigjointspeed(int index, ::google::protobuf::int32 value);
  inline void add_robotreducedconfigjointspeed(::google::protobuf::int32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      robotreducedconfigjointspeed() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_robotreducedconfigjointspeed();

  // required int32 robotReducedConfigTcpSpeed = 2;
  inline bool has_robotreducedconfigtcpspeed() const;
  inline void clear_robotreducedconfigtcpspeed();
  static const int kRobotReducedConfigTcpSpeedFieldNumber = 2;
  inline ::google::protobuf::int32 robotreducedconfigtcpspeed() const;
  inline void set_robotreducedconfigtcpspeed(::google::protobuf::int32 value);

  // required int32 robotReducedConfigTcpForce = 3;
  inline bool has_robotreducedconfigtcpforce() const;
  inline void clear_robotreducedconfigtcpforce();
  static const int kRobotReducedConfigTcpForceFieldNumber = 3;
  inline ::google::protobuf::int32 robotreducedconfigtcpforce() const;
  inline void set_robotreducedconfigtcpforce(::google::protobuf::int32 value);

  // required int32 robotReducedConfigMomentum = 4;
  inline bool has_robotreducedconfigmomentum() const;
  inline void clear_robotreducedconfigmomentum();
  static const int kRobotReducedConfigMomentumFieldNumber = 4;
  inline ::google::protobuf::int32 robotreducedconfigmomentum() const;
  inline void set_robotreducedconfigmomentum(::google::protobuf::int32 value);

  // required int32 robotReducedConfigPower = 5;
  inline bool has_robotreducedconfigpower() const;
  inline void clear_robotreducedconfigpower();
  static const int kRobotReducedConfigPowerFieldNumber = 5;
  inline ::google::protobuf::int32 robotreducedconfigpower() const;
  inline void set_robotreducedconfigpower(::google::protobuf::int32 value);

  // required int32 robotSafeguradResetConfig = 6;
  inline bool has_robotsafeguradresetconfig() const;
  inline void clear_robotsafeguradresetconfig();
  static const int kRobotSafeguradResetConfigFieldNumber = 6;
  inline ::google::protobuf::int32 robotsafeguradresetconfig() const;
  inline void set_robotsafeguradresetconfig(::google::protobuf::int32 value);

  // required int32 robotOperationalModeConfig = 7;
  inline bool has_robotoperationalmodeconfig() const;
  inline void clear_robotoperationalmodeconfig();
  static const int kRobotOperationalModeConfigFieldNumber = 7;
  inline ::google::protobuf::int32 robotoperationalmodeconfig() const;
  inline void set_robotoperationalmodeconfig(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:aubo.robot.common.ProtoRobotSafetyConfig)
 private:
  inline void set_has_robotreducedconfigtcpspeed();
  inline void clear_has_robotreducedconfigtcpspeed();
  inline void set_has_robotreducedconfigtcpforce();
  inline void clear_has_robotreducedconfigtcpforce();
  inline void set_has_robotreducedconfigmomentum();
  inline void clear_has_robotreducedconfigmomentum();
  inline void set_has_robotreducedconfigpower();
  inline void clear_has_robotreducedconfigpower();
  inline void set_has_robotsafeguradresetconfig();
  inline void clear_has_robotsafeguradresetconfig();
  inline void set_has_robotoperationalmodeconfig();
  inline void clear_has_robotoperationalmodeconfig();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > robotreducedconfigjointspeed_;
  ::google::protobuf::int32 robotreducedconfigtcpspeed_;
  ::google::protobuf::int32 robotreducedconfigtcpforce_;
  ::google::protobuf::int32 robotreducedconfigmomentum_;
  ::google::protobuf::int32 robotreducedconfigpower_;
  ::google::protobuf::int32 robotsafeguradresetconfig_;
  ::google::protobuf::int32 robotoperationalmodeconfig_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static ProtoRobotSafetyConfig* default_instance_;
};
// -------------------------------------------------------------------

class ProtoResponseRobotSafetyConfig : public ::google::protobuf::Message {
 public:
  ProtoResponseRobotSafetyConfig();
  virtual ~ProtoResponseRobotSafetyConfig();

  ProtoResponseRobotSafetyConfig(const ProtoResponseRobotSafetyConfig& from);

  inline ProtoResponseRobotSafetyConfig& operator=(const ProtoResponseRobotSafetyConfig& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoResponseRobotSafetyConfig& default_instance();

  void Swap(ProtoResponseRobotSafetyConfig* other);

  // implements Message ----------------------------------------------

  ProtoResponseRobotSafetyConfig* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoResponseRobotSafetyConfig& from);
  void MergeFrom(const ProtoResponseRobotSafetyConfig& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .aubo.robot.common.ProtoRobotSafetyConfig safetyConfig = 1;
  inline int safetyconfig_size() const;
  inline void clear_safetyconfig();
  static const int kSafetyConfigFieldNumber = 1;
  inline const ::aubo::robot::common::ProtoRobotSafetyConfig& safetyconfig(int index) const;
  inline ::aubo::robot::common::ProtoRobotSafetyConfig* mutable_safetyconfig(int index);
  inline ::aubo::robot::common::ProtoRobotSafetyConfig* add_safetyconfig();
  inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ProtoRobotSafetyConfig >&
      safetyconfig() const;
  inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ProtoRobotSafetyConfig >*
      mutable_safetyconfig();

  // required .aubo.robot.common.RobotCommonResponse errorInfo = 2;
  inline bool has_errorinfo() const;
  inline void clear_errorinfo();
  static const int kErrorInfoFieldNumber = 2;
  inline const ::aubo::robot::common::RobotCommonResponse& errorinfo() const;
  inline ::aubo::robot::common::RobotCommonResponse* mutable_errorinfo();
  inline ::aubo::robot::common::RobotCommonResponse* release_errorinfo();
  inline void set_allocated_errorinfo(::aubo::robot::common::RobotCommonResponse* errorinfo);

  // @@protoc_insertion_point(class_scope:aubo.robot.common.ProtoResponseRobotSafetyConfig)
 private:
  inline void set_has_errorinfo();
  inline void clear_has_errorinfo();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ProtoRobotSafetyConfig > safetyconfig_;
  ::aubo::robot::common::RobotCommonResponse* errorinfo_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static ProtoResponseRobotSafetyConfig* default_instance_;
};
// -------------------------------------------------------------------

class ProtoOrpeSafetyStatus : public ::google::protobuf::Message {
 public:
  ProtoOrpeSafetyStatus();
  virtual ~ProtoOrpeSafetyStatus();

  ProtoOrpeSafetyStatus(const ProtoOrpeSafetyStatus& from);

  inline ProtoOrpeSafetyStatus& operator=(const ProtoOrpeSafetyStatus& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoOrpeSafetyStatus& default_instance();

  void Swap(ProtoOrpeSafetyStatus* other);

  // implements Message ----------------------------------------------

  ProtoOrpeSafetyStatus* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoOrpeSafetyStatus& from);
  void MergeFrom(const ProtoOrpeSafetyStatus& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int32 orpePause = 1;
  inline bool has_orpepause() const;
  inline void clear_orpepause();
  static const int kOrpePauseFieldNumber = 1;
  inline ::google::protobuf::int32 orpepause() const;
  inline void set_orpepause(::google::protobuf::int32 value);

  // required int32 orpeStop = 2;
  inline bool has_orpestop() const;
  inline void clear_orpestop();
  static const int kOrpeStopFieldNumber = 2;
  inline ::google::protobuf::int32 orpestop() const;
  inline void set_orpestop(::google::protobuf::int32 value);

  // repeated uint32 orpeError = 3;
  inline int orpeerror_size() const;
  inline void clear_orpeerror();
  static const int kOrpeErrorFieldNumber = 3;
  inline ::google::protobuf::uint32 orpeerror(int index) const;
  inline void set_orpeerror(int index, ::google::protobuf::uint32 value);
  inline void add_orpeerror(::google::protobuf::uint32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      orpeerror() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_orpeerror();

  // required int32 systemEmergencyStop = 4;
  inline bool has_systememergencystop() const;
  inline void clear_systememergencystop();
  static const int kSystemEmergencyStopFieldNumber = 4;
  inline ::google::protobuf::int32 systememergencystop() const;
  inline void set_systememergencystop(::google::protobuf::int32 value);

  // required int32 reducedModeError = 5;
  inline bool has_reducedmodeerror() const;
  inline void clear_reducedmodeerror();
  static const int kReducedModeErrorFieldNumber = 5;
  inline ::google::protobuf::int32 reducedmodeerror() const;
  inline void set_reducedmodeerror(::google::protobuf::int32 value);

  // required int32 safetyguardResetSucc = 6;
  inline bool has_safetyguardresetsucc() const;
  inline void clear_safetyguardresetsucc();
  static const int kSafetyguardResetSuccFieldNumber = 6;
  inline ::google::protobuf::int32 safetyguardresetsucc() const;
  inline void set_safetyguardresetsucc(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:aubo.robot.common.ProtoOrpeSafetyStatus)
 private:
  inline void set_has_orpepause();
  inline void clear_has_orpepause();
  inline void set_has_orpestop();
  inline void clear_has_orpestop();
  inline void set_has_systememergencystop();
  inline void clear_has_systememergencystop();
  inline void set_has_reducedmodeerror();
  inline void clear_has_reducedmodeerror();
  inline void set_has_safetyguardresetsucc();
  inline void clear_has_safetyguardresetsucc();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::int32 orpepause_;
  ::google::protobuf::int32 orpestop_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > orpeerror_;
  ::google::protobuf::int32 systememergencystop_;
  ::google::protobuf::int32 reducedmodeerror_;
  ::google::protobuf::int32 safetyguardresetsucc_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static ProtoOrpeSafetyStatus* default_instance_;
};
// -------------------------------------------------------------------

class ProtoResponseOrpeSafetyStatus : public ::google::protobuf::Message {
 public:
  ProtoResponseOrpeSafetyStatus();
  virtual ~ProtoResponseOrpeSafetyStatus();

  ProtoResponseOrpeSafetyStatus(const ProtoResponseOrpeSafetyStatus& from);

  inline ProtoResponseOrpeSafetyStatus& operator=(const ProtoResponseOrpeSafetyStatus& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoResponseOrpeSafetyStatus& default_instance();

  void Swap(ProtoResponseOrpeSafetyStatus* other);

  // implements Message ----------------------------------------------

  ProtoResponseOrpeSafetyStatus* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoResponseOrpeSafetyStatus& from);
  void MergeFrom(const ProtoResponseOrpeSafetyStatus& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .aubo.robot.common.ProtoOrpeSafetyStatus safetyStatus = 1;
  inline int safetystatus_size() const;
  inline void clear_safetystatus();
  static const int kSafetyStatusFieldNumber = 1;
  inline const ::aubo::robot::common::ProtoOrpeSafetyStatus& safetystatus(int index) const;
  inline ::aubo::robot::common::ProtoOrpeSafetyStatus* mutable_safetystatus(int index);
  inline ::aubo::robot::common::ProtoOrpeSafetyStatus* add_safetystatus();
  inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ProtoOrpeSafetyStatus >&
      safetystatus() const;
  inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ProtoOrpeSafetyStatus >*
      mutable_safetystatus();

  // required .aubo.robot.common.RobotCommonResponse errorInfo = 2;
  inline bool has_errorinfo() const;
  inline void clear_errorinfo();
  static const int kErrorInfoFieldNumber = 2;
  inline const ::aubo::robot::common::RobotCommonResponse& errorinfo() const;
  inline ::aubo::robot::common::RobotCommonResponse* mutable_errorinfo();
  inline ::aubo::robot::common::RobotCommonResponse* release_errorinfo();
  inline void set_allocated_errorinfo(::aubo::robot::common::RobotCommonResponse* errorinfo);

  // @@protoc_insertion_point(class_scope:aubo.robot.common.ProtoResponseOrpeSafetyStatus)
 private:
  inline void set_has_errorinfo();
  inline void clear_has_errorinfo();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ProtoOrpeSafetyStatus > safetystatus_;
  ::aubo::robot::common::RobotCommonResponse* errorinfo_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static ProtoResponseOrpeSafetyStatus* default_instance_;
};
// -------------------------------------------------------------------

class DhParam : public ::google::protobuf::Message {
 public:
  DhParam();
  virtual ~DhParam();

  DhParam(const DhParam& from);

  inline DhParam& operator=(const DhParam& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DhParam& default_instance();

  void Swap(DhParam* other);

  // implements Message ----------------------------------------------

  DhParam* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const DhParam& from);
  void MergeFrom(const DhParam& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required double A3 = 1;
  inline bool has_a3() const;
  inline void clear_a3();
  static const int kA3FieldNumber = 1;
  inline double a3() const;
  inline void set_a3(double value);

  // required double A4 = 2;
  inline bool has_a4() const;
  inline void clear_a4();
  static const int kA4FieldNumber = 2;
  inline double a4() const;
  inline void set_a4(double value);

  // required double D1 = 3;
  inline bool has_d1() const;
  inline void clear_d1();
  static const int kD1FieldNumber = 3;
  inline double d1() const;
  inline void set_d1(double value);

  // required double D2 = 4;
  inline bool has_d2() const;
  inline void clear_d2();
  static const int kD2FieldNumber = 4;
  inline double d2() const;
  inline void set_d2(double value);

  // required double D5 = 5;
  inline bool has_d5() const;
  inline void clear_d5();
  static const int kD5FieldNumber = 5;
  inline double d5() const;
  inline void set_d5(double value);

  // required double D6 = 6;
  inline bool has_d6() const;
  inline void clear_d6();
  static const int kD6FieldNumber = 6;
  inline double d6() const;
  inline void set_d6(double value);

  // repeated double alpha = 7;
  inline int alpha_size() const;
  inline void clear_alpha();
  static const int kAlphaFieldNumber = 7;
  inline double alpha(int index) const;
  inline void set_alpha(int index, double value);
  inline void add_alpha(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      alpha() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_alpha();

  // repeated double a = 8;
  inline int a_size() const;
  inline void clear_a();
  static const int kAFieldNumber = 8;
  inline double a(int index) const;
  inline void set_a(int index, double value);
  inline void add_a(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      a() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_a();

  // repeated double d = 9;
  inline int d_size() const;
  inline void clear_d();
  static const int kDFieldNumber = 9;
  inline double d(int index) const;
  inline void set_d(int index, double value);
  inline void add_d(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      d() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_d();

  // repeated double theta = 10;
  inline int theta_size() const;
  inline void clear_theta();
  static const int kThetaFieldNumber = 10;
  inline double theta(int index) const;
  inline void set_theta(int index, double value);
  inline void add_theta(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      theta() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_theta();

  // @@protoc_insertion_point(class_scope:aubo.robot.common.DhParam)
 private:
  inline void set_has_a3();
  inline void clear_has_a3();
  inline void set_has_a4();
  inline void clear_has_a4();
  inline void set_has_d1();
  inline void clear_has_d1();
  inline void set_has_d2();
  inline void clear_has_d2();
  inline void set_has_d5();
  inline void clear_has_d5();
  inline void set_has_d6();
  inline void clear_has_d6();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  double a3_;
  double a4_;
  double d1_;
  double d2_;
  double d5_;
  double d6_;
  ::google::protobuf::RepeatedField< double > alpha_;
  ::google::protobuf::RepeatedField< double > a_;
  ::google::protobuf::RepeatedField< double > d_;
  ::google::protobuf::RepeatedField< double > theta_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static DhParam* default_instance_;
};
// -------------------------------------------------------------------

class ProtoResponseRobotDhParam : public ::google::protobuf::Message {
 public:
  ProtoResponseRobotDhParam();
  virtual ~ProtoResponseRobotDhParam();

  ProtoResponseRobotDhParam(const ProtoResponseRobotDhParam& from);

  inline ProtoResponseRobotDhParam& operator=(const ProtoResponseRobotDhParam& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoResponseRobotDhParam& default_instance();

  void Swap(ProtoResponseRobotDhParam* other);

  // implements Message ----------------------------------------------

  ProtoResponseRobotDhParam* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoResponseRobotDhParam& from);
  void MergeFrom(const ProtoResponseRobotDhParam& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int32 robotType = 1;
  inline bool has_robottype() const;
  inline void clear_robottype();
  static const int kRobotTypeFieldNumber = 1;
  inline ::google::protobuf::int32 robottype() const;
  inline void set_robottype(::google::protobuf::int32 value);

  // required .aubo.robot.common.DhParam dhParam = 2;
  inline bool has_dhparam() const;
  inline void clear_dhparam();
  static const int kDhParamFieldNumber = 2;
  inline const ::aubo::robot::common::DhParam& dhparam() const;
  inline ::aubo::robot::common::DhParam* mutable_dhparam();
  inline ::aubo::robot::common::DhParam* release_dhparam();
  inline void set_allocated_dhparam(::aubo::robot::common::DhParam* dhparam);

  // required .aubo.robot.common.RobotCommonResponse errorInfo = 3;
  inline bool has_errorinfo() const;
  inline void clear_errorinfo();
  static const int kErrorInfoFieldNumber = 3;
  inline const ::aubo::robot::common::RobotCommonResponse& errorinfo() const;
  inline ::aubo::robot::common::RobotCommonResponse* mutable_errorinfo();
  inline ::aubo::robot::common::RobotCommonResponse* release_errorinfo();
  inline void set_allocated_errorinfo(::aubo::robot::common::RobotCommonResponse* errorinfo);

  // @@protoc_insertion_point(class_scope:aubo.robot.common.ProtoResponseRobotDhParam)
 private:
  inline void set_has_robottype();
  inline void clear_has_robottype();
  inline void set_has_dhparam();
  inline void clear_has_dhparam();
  inline void set_has_errorinfo();
  inline void clear_has_errorinfo();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::aubo::robot::common::DhParam* dhparam_;
  ::aubo::robot::common::RobotCommonResponse* errorinfo_;
  ::google::protobuf::int32 robottype_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static ProtoResponseRobotDhParam* default_instance_;
};
// -------------------------------------------------------------------

class RobotJointOffset : public ::google::protobuf::Message {
 public:
  RobotJointOffset();
  virtual ~RobotJointOffset();

  RobotJointOffset(const RobotJointOffset& from);

  inline RobotJointOffset& operator=(const RobotJointOffset& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RobotJointOffset& default_instance();

  void Swap(RobotJointOffset* other);

  // implements Message ----------------------------------------------

  RobotJointOffset* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RobotJointOffset& from);
  void MergeFrom(const RobotJointOffset& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required double joint1 = 1;
  inline bool has_joint1() const;
  inline void clear_joint1();
  static const int kJoint1FieldNumber = 1;
  inline double joint1() const;
  inline void set_joint1(double value);

  // required double joint2 = 2;
  inline bool has_joint2() const;
  inline void clear_joint2();
  static const int kJoint2FieldNumber = 2;
  inline double joint2() const;
  inline void set_joint2(double value);

  // required double joint3 = 3;
  inline bool has_joint3() const;
  inline void clear_joint3();
  static const int kJoint3FieldNumber = 3;
  inline double joint3() const;
  inline void set_joint3(double value);

  // required double joint4 = 4;
  inline bool has_joint4() const;
  inline void clear_joint4();
  static const int kJoint4FieldNumber = 4;
  inline double joint4() const;
  inline void set_joint4(double value);

  // required double joint5 = 5;
  inline bool has_joint5() const;
  inline void clear_joint5();
  static const int kJoint5FieldNumber = 5;
  inline double joint5() const;
  inline void set_joint5(double value);

  // required double joint6 = 6;
  inline bool has_joint6() const;
  inline void clear_joint6();
  static const int kJoint6FieldNumber = 6;
  inline double joint6() const;
  inline void set_joint6(double value);

  // @@protoc_insertion_point(class_scope:aubo.robot.common.RobotJointOffset)
 private:
  inline void set_has_joint1();
  inline void clear_has_joint1();
  inline void set_has_joint2();
  inline void clear_has_joint2();
  inline void set_has_joint3();
  inline void clear_has_joint3();
  inline void set_has_joint4();
  inline void clear_has_joint4();
  inline void set_has_joint5();
  inline void clear_has_joint5();
  inline void set_has_joint6();
  inline void clear_has_joint6();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  double joint1_;
  double joint2_;
  double joint3_;
  double joint4_;
  double joint5_;
  double joint6_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static RobotJointOffset* default_instance_;
};
// -------------------------------------------------------------------

class ProtoRobotMoveFuncResult : public ::google::protobuf::Message {
 public:
  ProtoRobotMoveFuncResult();
  virtual ~ProtoRobotMoveFuncResult();

  ProtoRobotMoveFuncResult(const ProtoRobotMoveFuncResult& from);

  inline ProtoRobotMoveFuncResult& operator=(const ProtoRobotMoveFuncResult& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoRobotMoveFuncResult& default_instance();

  void Swap(ProtoRobotMoveFuncResult* other);

  // implements Message ----------------------------------------------

  ProtoRobotMoveFuncResult* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoRobotMoveFuncResult& from);
  void MergeFrom(const ProtoRobotMoveFuncResult& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int32 ret = 1;
  inline bool has_ret() const;
  inline void clear_ret();
  static const int kRetFieldNumber = 1;
  inline ::google::protobuf::int32 ret() const;
  inline void set_ret(::google::protobuf::int32 value);

  // required .aubo.robot.common.RobotCommonResponse errorInfo = 2;
  inline bool has_errorinfo() const;
  inline void clear_errorinfo();
  static const int kErrorInfoFieldNumber = 2;
  inline const ::aubo::robot::common::RobotCommonResponse& errorinfo() const;
  inline ::aubo::robot::common::RobotCommonResponse* mutable_errorinfo();
  inline ::aubo::robot::common::RobotCommonResponse* release_errorinfo();
  inline void set_allocated_errorinfo(::aubo::robot::common::RobotCommonResponse* errorinfo);

  // @@protoc_insertion_point(class_scope:aubo.robot.common.ProtoRobotMoveFuncResult)
 private:
  inline void set_has_ret();
  inline void clear_has_ret();
  inline void set_has_errorinfo();
  inline void clear_has_errorinfo();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::aubo::robot::common::RobotCommonResponse* errorinfo_;
  ::google::protobuf::int32 ret_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static ProtoRobotMoveFuncResult* default_instance_;
};
// -------------------------------------------------------------------

class ProtoSeamTrack_t : public ::google::protobuf::Message {
 public:
  ProtoSeamTrack_t();
  virtual ~ProtoSeamTrack_t();

  ProtoSeamTrack_t(const ProtoSeamTrack_t& from);

  inline ProtoSeamTrack_t& operator=(const ProtoSeamTrack_t& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoSeamTrack_t& default_instance();

  void Swap(ProtoSeamTrack_t* other);

  // implements Message ----------------------------------------------

  ProtoSeamTrack_t* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoSeamTrack_t& from);
  void MergeFrom(const ProtoSeamTrack_t& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int32 trackEnable = 1;
  inline bool has_trackenable() const;
  inline void clear_trackenable();
  static const int kTrackEnableFieldNumber = 1;
  inline ::google::protobuf::int32 trackenable() const;
  inline void set_trackenable(::google::protobuf::int32 value);

  // required .aubo.robot.common.ProtoRoadPoint currentRoadPoint = 2;
  inline bool has_currentroadpoint() const;
  inline void clear_currentroadpoint();
  static const int kCurrentRoadPointFieldNumber = 2;
  inline const ::aubo::robot::common::ProtoRoadPoint& currentroadpoint() const;
  inline ::aubo::robot::common::ProtoRoadPoint* mutable_currentroadpoint();
  inline ::aubo::robot::common::ProtoRoadPoint* release_currentroadpoint();
  inline void set_allocated_currentroadpoint(::aubo::robot::common::ProtoRoadPoint* currentroadpoint);

  // required .aubo.robot.common.ProtoRoadPoint nextRoadPoint = 3;
  inline bool has_nextroadpoint() const;
  inline void clear_nextroadpoint();
  static const int kNextRoadPointFieldNumber = 3;
  inline const ::aubo::robot::common::ProtoRoadPoint& nextroadpoint() const;
  inline ::aubo::robot::common::ProtoRoadPoint* mutable_nextroadpoint();
  inline ::aubo::robot::common::ProtoRoadPoint* release_nextroadpoint();
  inline void set_allocated_nextroadpoint(::aubo::robot::common::ProtoRoadPoint* nextroadpoint);

  // required int32 timeInterval = 4;
  inline bool has_timeinterval() const;
  inline void clear_timeinterval();
  static const int kTimeIntervalFieldNumber = 4;
  inline ::google::protobuf::int32 timeinterval() const;
  inline void set_timeinterval(::google::protobuf::int32 value);

  // repeated double currentPosError = 5;
  inline int currentposerror_size() const;
  inline void clear_currentposerror();
  static const int kCurrentPosErrorFieldNumber = 5;
  inline double currentposerror(int index) const;
  inline void set_currentposerror(int index, double value);
  inline void add_currentposerror(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      currentposerror() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_currentposerror();

  // required double maxVel = 6;
  inline bool has_maxvel() const;
  inline void clear_maxvel();
  static const int kMaxVelFieldNumber = 6;
  inline double maxvel() const;
  inline void set_maxvel(double value);

  // required double maxAcc = 7;
  inline bool has_maxacc() const;
  inline void clear_maxacc();
  static const int kMaxAccFieldNumber = 7;
  inline double maxacc() const;
  inline void set_maxacc(double value);

  // required int32 paraChanged = 8;
  inline bool has_parachanged() const;
  inline void clear_parachanged();
  static const int kParaChangedFieldNumber = 8;
  inline ::google::protobuf::int32 parachanged() const;
  inline void set_parachanged(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:aubo.robot.common.ProtoSeamTrack_t)
 private:
  inline void set_has_trackenable();
  inline void clear_has_trackenable();
  inline void set_has_currentroadpoint();
  inline void clear_has_currentroadpoint();
  inline void set_has_nextroadpoint();
  inline void clear_has_nextroadpoint();
  inline void set_has_timeinterval();
  inline void clear_has_timeinterval();
  inline void set_has_maxvel();
  inline void clear_has_maxvel();
  inline void set_has_maxacc();
  inline void clear_has_maxacc();
  inline void set_has_parachanged();
  inline void clear_has_parachanged();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::aubo::robot::common::ProtoRoadPoint* currentroadpoint_;
  ::google::protobuf::int32 trackenable_;
  ::google::protobuf::int32 timeinterval_;
  ::aubo::robot::common::ProtoRoadPoint* nextroadpoint_;
  ::google::protobuf::RepeatedField< double > currentposerror_;
  double maxvel_;
  double maxacc_;
  ::google::protobuf::int32 parachanged_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static ProtoSeamTrack_t* default_instance_;
};
// -------------------------------------------------------------------

class ProtoSeamTrackResponse_t : public ::google::protobuf::Message {
 public:
  ProtoSeamTrackResponse_t();
  virtual ~ProtoSeamTrackResponse_t();

  ProtoSeamTrackResponse_t(const ProtoSeamTrackResponse_t& from);

  inline ProtoSeamTrackResponse_t& operator=(const ProtoSeamTrackResponse_t& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoSeamTrackResponse_t& default_instance();

  void Swap(ProtoSeamTrackResponse_t* other);

  // implements Message ----------------------------------------------

  ProtoSeamTrackResponse_t* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoSeamTrackResponse_t& from);
  void MergeFrom(const ProtoSeamTrackResponse_t& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .aubo.robot.common.ProtoSeamTrack_t seamTrack = 1;
  inline bool has_seamtrack() const;
  inline void clear_seamtrack();
  static const int kSeamTrackFieldNumber = 1;
  inline const ::aubo::robot::common::ProtoSeamTrack_t& seamtrack() const;
  inline ::aubo::robot::common::ProtoSeamTrack_t* mutable_seamtrack();
  inline ::aubo::robot::common::ProtoSeamTrack_t* release_seamtrack();
  inline void set_allocated_seamtrack(::aubo::robot::common::ProtoSeamTrack_t* seamtrack);

  // required .aubo.robot.common.RobotCommonResponse errorInfo = 2;
  inline bool has_errorinfo() const;
  inline void clear_errorinfo();
  static const int kErrorInfoFieldNumber = 2;
  inline const ::aubo::robot::common::RobotCommonResponse& errorinfo() const;
  inline ::aubo::robot::common::RobotCommonResponse* mutable_errorinfo();
  inline ::aubo::robot::common::RobotCommonResponse* release_errorinfo();
  inline void set_allocated_errorinfo(::aubo::robot::common::RobotCommonResponse* errorinfo);

  // @@protoc_insertion_point(class_scope:aubo.robot.common.ProtoSeamTrackResponse_t)
 private:
  inline void set_has_seamtrack();
  inline void clear_has_seamtrack();
  inline void set_has_errorinfo();
  inline void clear_has_errorinfo();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::aubo::robot::common::ProtoSeamTrack_t* seamtrack_;
  ::aubo::robot::common::RobotCommonResponse* errorinfo_;
  friend void  protobuf_AddDesc_robotcommon_2eproto();
  friend void protobuf_AssignDesc_robotcommon_2eproto();
  friend void protobuf_ShutdownFile_robotcommon_2eproto();

  void InitAsDefaultInstance();
  static ProtoSeamTrackResponse_t* default_instance_;
};
// ===================================================================


// ===================================================================

// Pos

// required double x = 1;
inline bool Pos::has_x() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void Pos::set_has_x() {
  _has_bits_[0] |= 0x00000001u;
}
inline void Pos::clear_has_x() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void Pos::clear_x() {
  x_ = 0;
  clear_has_x();
}
inline double Pos::x() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.Pos.x)
  return x_;
}
inline void Pos::set_x(double value) {
  set_has_x();
  x_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.Pos.x)
}

// required double y = 2;
inline bool Pos::has_y() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void Pos::set_has_y() {
  _has_bits_[0] |= 0x00000002u;
}
inline void Pos::clear_has_y() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void Pos::clear_y() {
  y_ = 0;
  clear_has_y();
}
inline double Pos::y() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.Pos.y)
  return y_;
}
inline void Pos::set_y(double value) {
  set_has_y();
  y_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.Pos.y)
}

// required double z = 3;
inline bool Pos::has_z() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void Pos::set_has_z() {
  _has_bits_[0] |= 0x00000004u;
}
inline void Pos::clear_has_z() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void Pos::clear_z() {
  z_ = 0;
  clear_has_z();
}
inline double Pos::z() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.Pos.z)
  return z_;
}
inline void Pos::set_z(double value) {
  set_has_z();
  z_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.Pos.z)
}

// -------------------------------------------------------------------

// cartesianPos_U

// repeated .aubo.robot.common.Pos position = 1;
inline int cartesianPos_U::position_size() const {
  return position_.size();
}
inline void cartesianPos_U::clear_position() {
  position_.Clear();
}
inline const ::aubo::robot::common::Pos& cartesianPos_U::position(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.cartesianPos_U.position)
  return position_.Get(index);
}
inline ::aubo::robot::common::Pos* cartesianPos_U::mutable_position(int index) {
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.cartesianPos_U.position)
  return position_.Mutable(index);
}
inline ::aubo::robot::common::Pos* cartesianPos_U::add_position() {
  // @@protoc_insertion_point(field_add:aubo.robot.common.cartesianPos_U.position)
  return position_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::Pos >&
cartesianPos_U::position() const {
  // @@protoc_insertion_point(field_list:aubo.robot.common.cartesianPos_U.position)
  return position_;
}
inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::Pos >*
cartesianPos_U::mutable_position() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.common.cartesianPos_U.position)
  return &position_;
}

// repeated double positionVector = 2;
inline int cartesianPos_U::positionvector_size() const {
  return positionvector_.size();
}
inline void cartesianPos_U::clear_positionvector() {
  positionvector_.Clear();
}
inline double cartesianPos_U::positionvector(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.cartesianPos_U.positionVector)
  return positionvector_.Get(index);
}
inline void cartesianPos_U::set_positionvector(int index, double value) {
  positionvector_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.common.cartesianPos_U.positionVector)
}
inline void cartesianPos_U::add_positionvector(double value) {
  positionvector_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.common.cartesianPos_U.positionVector)
}
inline const ::google::protobuf::RepeatedField< double >&
cartesianPos_U::positionvector() const {
  // @@protoc_insertion_point(field_list:aubo.robot.common.cartesianPos_U.positionVector)
  return positionvector_;
}
inline ::google::protobuf::RepeatedField< double >*
cartesianPos_U::mutable_positionvector() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.common.cartesianPos_U.positionVector)
  return &positionvector_;
}

// -------------------------------------------------------------------

// Ori

// required double w = 1;
inline bool Ori::has_w() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void Ori::set_has_w() {
  _has_bits_[0] |= 0x00000001u;
}
inline void Ori::clear_has_w() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void Ori::clear_w() {
  w_ = 0;
  clear_has_w();
}
inline double Ori::w() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.Ori.w)
  return w_;
}
inline void Ori::set_w(double value) {
  set_has_w();
  w_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.Ori.w)
}

// required double x = 2;
inline bool Ori::has_x() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void Ori::set_has_x() {
  _has_bits_[0] |= 0x00000002u;
}
inline void Ori::clear_has_x() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void Ori::clear_x() {
  x_ = 0;
  clear_has_x();
}
inline double Ori::x() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.Ori.x)
  return x_;
}
inline void Ori::set_x(double value) {
  set_has_x();
  x_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.Ori.x)
}

// required double y = 3;
inline bool Ori::has_y() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void Ori::set_has_y() {
  _has_bits_[0] |= 0x00000004u;
}
inline void Ori::clear_has_y() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void Ori::clear_y() {
  y_ = 0;
  clear_has_y();
}
inline double Ori::y() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.Ori.y)
  return y_;
}
inline void Ori::set_y(double value) {
  set_has_y();
  y_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.Ori.y)
}

// required double z = 4;
inline bool Ori::has_z() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void Ori::set_has_z() {
  _has_bits_[0] |= 0x00000008u;
}
inline void Ori::clear_has_z() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void Ori::clear_z() {
  z_ = 0;
  clear_has_z();
}
inline double Ori::z() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.Ori.z)
  return z_;
}
inline void Ori::set_z(double value) {
  set_has_z();
  z_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.Ori.z)
}

// -------------------------------------------------------------------

// cartesianOri_U

// repeated .aubo.robot.common.Ori orientation = 1;
inline int cartesianOri_U::orientation_size() const {
  return orientation_.size();
}
inline void cartesianOri_U::clear_orientation() {
  orientation_.Clear();
}
inline const ::aubo::robot::common::Ori& cartesianOri_U::orientation(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.cartesianOri_U.orientation)
  return orientation_.Get(index);
}
inline ::aubo::robot::common::Ori* cartesianOri_U::mutable_orientation(int index) {
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.cartesianOri_U.orientation)
  return orientation_.Mutable(index);
}
inline ::aubo::robot::common::Ori* cartesianOri_U::add_orientation() {
  // @@protoc_insertion_point(field_add:aubo.robot.common.cartesianOri_U.orientation)
  return orientation_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::Ori >&
cartesianOri_U::orientation() const {
  // @@protoc_insertion_point(field_list:aubo.robot.common.cartesianOri_U.orientation)
  return orientation_;
}
inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::Ori >*
cartesianOri_U::mutable_orientation() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.common.cartesianOri_U.orientation)
  return &orientation_;
}

// repeated double quaternionVector = 2;
inline int cartesianOri_U::quaternionvector_size() const {
  return quaternionvector_.size();
}
inline void cartesianOri_U::clear_quaternionvector() {
  quaternionvector_.Clear();
}
inline double cartesianOri_U::quaternionvector(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.cartesianOri_U.quaternionVector)
  return quaternionvector_.Get(index);
}
inline void cartesianOri_U::set_quaternionvector(int index, double value) {
  quaternionvector_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.common.cartesianOri_U.quaternionVector)
}
inline void cartesianOri_U::add_quaternionvector(double value) {
  quaternionvector_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.common.cartesianOri_U.quaternionVector)
}
inline const ::google::protobuf::RepeatedField< double >&
cartesianOri_U::quaternionvector() const {
  // @@protoc_insertion_point(field_list:aubo.robot.common.cartesianOri_U.quaternionVector)
  return quaternionvector_;
}
inline ::google::protobuf::RepeatedField< double >*
cartesianOri_U::mutable_quaternionvector() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.common.cartesianOri_U.quaternionVector)
  return &quaternionvector_;
}

// -------------------------------------------------------------------

// ProtoRoadPoint

// required .aubo.robot.common.cartesianPos_U cartPos = 1;
inline bool ProtoRoadPoint::has_cartpos() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoRoadPoint::set_has_cartpos() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoRoadPoint::clear_has_cartpos() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoRoadPoint::clear_cartpos() {
  if (cartpos_ != NULL) cartpos_->::aubo::robot::common::cartesianPos_U::Clear();
  clear_has_cartpos();
}
inline const ::aubo::robot::common::cartesianPos_U& ProtoRoadPoint::cartpos() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoRoadPoint.cartPos)
  return cartpos_ != NULL ? *cartpos_ : *default_instance_->cartpos_;
}
inline ::aubo::robot::common::cartesianPos_U* ProtoRoadPoint::mutable_cartpos() {
  set_has_cartpos();
  if (cartpos_ == NULL) cartpos_ = new ::aubo::robot::common::cartesianPos_U;
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ProtoRoadPoint.cartPos)
  return cartpos_;
}
inline ::aubo::robot::common::cartesianPos_U* ProtoRoadPoint::release_cartpos() {
  clear_has_cartpos();
  ::aubo::robot::common::cartesianPos_U* temp = cartpos_;
  cartpos_ = NULL;
  return temp;
}
inline void ProtoRoadPoint::set_allocated_cartpos(::aubo::robot::common::cartesianPos_U* cartpos) {
  delete cartpos_;
  cartpos_ = cartpos;
  if (cartpos) {
    set_has_cartpos();
  } else {
    clear_has_cartpos();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.ProtoRoadPoint.cartPos)
}

// required .aubo.robot.common.Ori orientation = 2;
inline bool ProtoRoadPoint::has_orientation() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoRoadPoint::set_has_orientation() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoRoadPoint::clear_has_orientation() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoRoadPoint::clear_orientation() {
  if (orientation_ != NULL) orientation_->::aubo::robot::common::Ori::Clear();
  clear_has_orientation();
}
inline const ::aubo::robot::common::Ori& ProtoRoadPoint::orientation() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoRoadPoint.orientation)
  return orientation_ != NULL ? *orientation_ : *default_instance_->orientation_;
}
inline ::aubo::robot::common::Ori* ProtoRoadPoint::mutable_orientation() {
  set_has_orientation();
  if (orientation_ == NULL) orientation_ = new ::aubo::robot::common::Ori;
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ProtoRoadPoint.orientation)
  return orientation_;
}
inline ::aubo::robot::common::Ori* ProtoRoadPoint::release_orientation() {
  clear_has_orientation();
  ::aubo::robot::common::Ori* temp = orientation_;
  orientation_ = NULL;
  return temp;
}
inline void ProtoRoadPoint::set_allocated_orientation(::aubo::robot::common::Ori* orientation) {
  delete orientation_;
  orientation_ = orientation;
  if (orientation) {
    set_has_orientation();
  } else {
    clear_has_orientation();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.ProtoRoadPoint.orientation)
}

// repeated double jointpos = 3;
inline int ProtoRoadPoint::jointpos_size() const {
  return jointpos_.size();
}
inline void ProtoRoadPoint::clear_jointpos() {
  jointpos_.Clear();
}
inline double ProtoRoadPoint::jointpos(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoRoadPoint.jointpos)
  return jointpos_.Get(index);
}
inline void ProtoRoadPoint::set_jointpos(int index, double value) {
  jointpos_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoRoadPoint.jointpos)
}
inline void ProtoRoadPoint::add_jointpos(double value) {
  jointpos_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.common.ProtoRoadPoint.jointpos)
}
inline const ::google::protobuf::RepeatedField< double >&
ProtoRoadPoint::jointpos() const {
  // @@protoc_insertion_point(field_list:aubo.robot.common.ProtoRoadPoint.jointpos)
  return jointpos_;
}
inline ::google::protobuf::RepeatedField< double >*
ProtoRoadPoint::mutable_jointpos() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.common.ProtoRoadPoint.jointpos)
  return &jointpos_;
}

// -------------------------------------------------------------------

// ProtoRoadPointResponse

// required .aubo.robot.common.ProtoRoadPoint waypoint = 1;
inline bool ProtoRoadPointResponse::has_waypoint() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoRoadPointResponse::set_has_waypoint() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoRoadPointResponse::clear_has_waypoint() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoRoadPointResponse::clear_waypoint() {
  if (waypoint_ != NULL) waypoint_->::aubo::robot::common::ProtoRoadPoint::Clear();
  clear_has_waypoint();
}
inline const ::aubo::robot::common::ProtoRoadPoint& ProtoRoadPointResponse::waypoint() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoRoadPointResponse.waypoint)
  return waypoint_ != NULL ? *waypoint_ : *default_instance_->waypoint_;
}
inline ::aubo::robot::common::ProtoRoadPoint* ProtoRoadPointResponse::mutable_waypoint() {
  set_has_waypoint();
  if (waypoint_ == NULL) waypoint_ = new ::aubo::robot::common::ProtoRoadPoint;
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ProtoRoadPointResponse.waypoint)
  return waypoint_;
}
inline ::aubo::robot::common::ProtoRoadPoint* ProtoRoadPointResponse::release_waypoint() {
  clear_has_waypoint();
  ::aubo::robot::common::ProtoRoadPoint* temp = waypoint_;
  waypoint_ = NULL;
  return temp;
}
inline void ProtoRoadPointResponse::set_allocated_waypoint(::aubo::robot::common::ProtoRoadPoint* waypoint) {
  delete waypoint_;
  waypoint_ = waypoint;
  if (waypoint) {
    set_has_waypoint();
  } else {
    clear_has_waypoint();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.ProtoRoadPointResponse.waypoint)
}

// repeated .aubo.robot.common.RobotCommonResponse errorInfo = 2;
inline int ProtoRoadPointResponse::errorinfo_size() const {
  return errorinfo_.size();
}
inline void ProtoRoadPointResponse::clear_errorinfo() {
  errorinfo_.Clear();
}
inline const ::aubo::robot::common::RobotCommonResponse& ProtoRoadPointResponse::errorinfo(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoRoadPointResponse.errorInfo)
  return errorinfo_.Get(index);
}
inline ::aubo::robot::common::RobotCommonResponse* ProtoRoadPointResponse::mutable_errorinfo(int index) {
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ProtoRoadPointResponse.errorInfo)
  return errorinfo_.Mutable(index);
}
inline ::aubo::robot::common::RobotCommonResponse* ProtoRoadPointResponse::add_errorinfo() {
  // @@protoc_insertion_point(field_add:aubo.robot.common.ProtoRoadPointResponse.errorInfo)
  return errorinfo_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::RobotCommonResponse >&
ProtoRoadPointResponse::errorinfo() const {
  // @@protoc_insertion_point(field_list:aubo.robot.common.ProtoRoadPointResponse.errorInfo)
  return errorinfo_;
}
inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::RobotCommonResponse >*
ProtoRoadPointResponse::mutable_errorinfo() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.common.ProtoRoadPointResponse.errorInfo)
  return &errorinfo_;
}

// -------------------------------------------------------------------

// ProtoJointAngle

// required double joint1 = 1;
inline bool ProtoJointAngle::has_joint1() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoJointAngle::set_has_joint1() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoJointAngle::clear_has_joint1() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoJointAngle::clear_joint1() {
  joint1_ = 0;
  clear_has_joint1();
}
inline double ProtoJointAngle::joint1() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoJointAngle.joint1)
  return joint1_;
}
inline void ProtoJointAngle::set_joint1(double value) {
  set_has_joint1();
  joint1_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoJointAngle.joint1)
}

// required double joint2 = 2;
inline bool ProtoJointAngle::has_joint2() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoJointAngle::set_has_joint2() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoJointAngle::clear_has_joint2() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoJointAngle::clear_joint2() {
  joint2_ = 0;
  clear_has_joint2();
}
inline double ProtoJointAngle::joint2() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoJointAngle.joint2)
  return joint2_;
}
inline void ProtoJointAngle::set_joint2(double value) {
  set_has_joint2();
  joint2_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoJointAngle.joint2)
}

// required double joint3 = 3;
inline bool ProtoJointAngle::has_joint3() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void ProtoJointAngle::set_has_joint3() {
  _has_bits_[0] |= 0x00000004u;
}
inline void ProtoJointAngle::clear_has_joint3() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void ProtoJointAngle::clear_joint3() {
  joint3_ = 0;
  clear_has_joint3();
}
inline double ProtoJointAngle::joint3() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoJointAngle.joint3)
  return joint3_;
}
inline void ProtoJointAngle::set_joint3(double value) {
  set_has_joint3();
  joint3_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoJointAngle.joint3)
}

// required double joint4 = 4;
inline bool ProtoJointAngle::has_joint4() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void ProtoJointAngle::set_has_joint4() {
  _has_bits_[0] |= 0x00000008u;
}
inline void ProtoJointAngle::clear_has_joint4() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void ProtoJointAngle::clear_joint4() {
  joint4_ = 0;
  clear_has_joint4();
}
inline double ProtoJointAngle::joint4() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoJointAngle.joint4)
  return joint4_;
}
inline void ProtoJointAngle::set_joint4(double value) {
  set_has_joint4();
  joint4_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoJointAngle.joint4)
}

// required double joint5 = 5;
inline bool ProtoJointAngle::has_joint5() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void ProtoJointAngle::set_has_joint5() {
  _has_bits_[0] |= 0x00000010u;
}
inline void ProtoJointAngle::clear_has_joint5() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void ProtoJointAngle::clear_joint5() {
  joint5_ = 0;
  clear_has_joint5();
}
inline double ProtoJointAngle::joint5() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoJointAngle.joint5)
  return joint5_;
}
inline void ProtoJointAngle::set_joint5(double value) {
  set_has_joint5();
  joint5_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoJointAngle.joint5)
}

// required double joint6 = 6;
inline bool ProtoJointAngle::has_joint6() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void ProtoJointAngle::set_has_joint6() {
  _has_bits_[0] |= 0x00000020u;
}
inline void ProtoJointAngle::clear_has_joint6() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void ProtoJointAngle::clear_joint6() {
  joint6_ = 0;
  clear_has_joint6();
}
inline double ProtoJointAngle::joint6() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoJointAngle.joint6)
  return joint6_;
}
inline void ProtoJointAngle::set_joint6(double value) {
  set_has_joint6();
  joint6_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoJointAngle.joint6)
}

// -------------------------------------------------------------------

// ProtoJointAngleResponse

// required .aubo.robot.common.ProtoJointAngle jointAngle = 1;
inline bool ProtoJointAngleResponse::has_jointangle() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoJointAngleResponse::set_has_jointangle() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoJointAngleResponse::clear_has_jointangle() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoJointAngleResponse::clear_jointangle() {
  if (jointangle_ != NULL) jointangle_->::aubo::robot::common::ProtoJointAngle::Clear();
  clear_has_jointangle();
}
inline const ::aubo::robot::common::ProtoJointAngle& ProtoJointAngleResponse::jointangle() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoJointAngleResponse.jointAngle)
  return jointangle_ != NULL ? *jointangle_ : *default_instance_->jointangle_;
}
inline ::aubo::robot::common::ProtoJointAngle* ProtoJointAngleResponse::mutable_jointangle() {
  set_has_jointangle();
  if (jointangle_ == NULL) jointangle_ = new ::aubo::robot::common::ProtoJointAngle;
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ProtoJointAngleResponse.jointAngle)
  return jointangle_;
}
inline ::aubo::robot::common::ProtoJointAngle* ProtoJointAngleResponse::release_jointangle() {
  clear_has_jointangle();
  ::aubo::robot::common::ProtoJointAngle* temp = jointangle_;
  jointangle_ = NULL;
  return temp;
}
inline void ProtoJointAngleResponse::set_allocated_jointangle(::aubo::robot::common::ProtoJointAngle* jointangle) {
  delete jointangle_;
  jointangle_ = jointangle;
  if (jointangle) {
    set_has_jointangle();
  } else {
    clear_has_jointangle();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.ProtoJointAngleResponse.jointAngle)
}

// repeated .aubo.robot.common.RobotCommonResponse errorInfo = 2;
inline int ProtoJointAngleResponse::errorinfo_size() const {
  return errorinfo_.size();
}
inline void ProtoJointAngleResponse::clear_errorinfo() {
  errorinfo_.Clear();
}
inline const ::aubo::robot::common::RobotCommonResponse& ProtoJointAngleResponse::errorinfo(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoJointAngleResponse.errorInfo)
  return errorinfo_.Get(index);
}
inline ::aubo::robot::common::RobotCommonResponse* ProtoJointAngleResponse::mutable_errorinfo(int index) {
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ProtoJointAngleResponse.errorInfo)
  return errorinfo_.Mutable(index);
}
inline ::aubo::robot::common::RobotCommonResponse* ProtoJointAngleResponse::add_errorinfo() {
  // @@protoc_insertion_point(field_add:aubo.robot.common.ProtoJointAngleResponse.errorInfo)
  return errorinfo_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::RobotCommonResponse >&
ProtoJointAngleResponse::errorinfo() const {
  // @@protoc_insertion_point(field_list:aubo.robot.common.ProtoJointAngleResponse.errorInfo)
  return errorinfo_;
}
inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::RobotCommonResponse >*
ProtoJointAngleResponse::mutable_errorinfo() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.common.ProtoJointAngleResponse.errorInfo)
  return &errorinfo_;
}

// -------------------------------------------------------------------

// ProtoForceSensorData

// required double data1 = 1;
inline bool ProtoForceSensorData::has_data1() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoForceSensorData::set_has_data1() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoForceSensorData::clear_has_data1() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoForceSensorData::clear_data1() {
  data1_ = 0;
  clear_has_data1();
}
inline double ProtoForceSensorData::data1() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoForceSensorData.data1)
  return data1_;
}
inline void ProtoForceSensorData::set_data1(double value) {
  set_has_data1();
  data1_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoForceSensorData.data1)
}

// required double data2 = 2;
inline bool ProtoForceSensorData::has_data2() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoForceSensorData::set_has_data2() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoForceSensorData::clear_has_data2() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoForceSensorData::clear_data2() {
  data2_ = 0;
  clear_has_data2();
}
inline double ProtoForceSensorData::data2() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoForceSensorData.data2)
  return data2_;
}
inline void ProtoForceSensorData::set_data2(double value) {
  set_has_data2();
  data2_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoForceSensorData.data2)
}

// required double data3 = 3;
inline bool ProtoForceSensorData::has_data3() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void ProtoForceSensorData::set_has_data3() {
  _has_bits_[0] |= 0x00000004u;
}
inline void ProtoForceSensorData::clear_has_data3() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void ProtoForceSensorData::clear_data3() {
  data3_ = 0;
  clear_has_data3();
}
inline double ProtoForceSensorData::data3() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoForceSensorData.data3)
  return data3_;
}
inline void ProtoForceSensorData::set_data3(double value) {
  set_has_data3();
  data3_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoForceSensorData.data3)
}

// required double data4 = 4;
inline bool ProtoForceSensorData::has_data4() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void ProtoForceSensorData::set_has_data4() {
  _has_bits_[0] |= 0x00000008u;
}
inline void ProtoForceSensorData::clear_has_data4() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void ProtoForceSensorData::clear_data4() {
  data4_ = 0;
  clear_has_data4();
}
inline double ProtoForceSensorData::data4() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoForceSensorData.data4)
  return data4_;
}
inline void ProtoForceSensorData::set_data4(double value) {
  set_has_data4();
  data4_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoForceSensorData.data4)
}

// required double data5 = 5;
inline bool ProtoForceSensorData::has_data5() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void ProtoForceSensorData::set_has_data5() {
  _has_bits_[0] |= 0x00000010u;
}
inline void ProtoForceSensorData::clear_has_data5() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void ProtoForceSensorData::clear_data5() {
  data5_ = 0;
  clear_has_data5();
}
inline double ProtoForceSensorData::data5() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoForceSensorData.data5)
  return data5_;
}
inline void ProtoForceSensorData::set_data5(double value) {
  set_has_data5();
  data5_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoForceSensorData.data5)
}

// required double data6 = 6;
inline bool ProtoForceSensorData::has_data6() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void ProtoForceSensorData::set_has_data6() {
  _has_bits_[0] |= 0x00000020u;
}
inline void ProtoForceSensorData::clear_has_data6() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void ProtoForceSensorData::clear_data6() {
  data6_ = 0;
  clear_has_data6();
}
inline double ProtoForceSensorData::data6() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoForceSensorData.data6)
  return data6_;
}
inline void ProtoForceSensorData::set_data6(double value) {
  set_has_data6();
  data6_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoForceSensorData.data6)
}

// -------------------------------------------------------------------

// ProtoForceSensorDataResponse

// required .aubo.robot.common.ProtoForceSensorData forceSensorData = 1;
inline bool ProtoForceSensorDataResponse::has_forcesensordata() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoForceSensorDataResponse::set_has_forcesensordata() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoForceSensorDataResponse::clear_has_forcesensordata() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoForceSensorDataResponse::clear_forcesensordata() {
  if (forcesensordata_ != NULL) forcesensordata_->::aubo::robot::common::ProtoForceSensorData::Clear();
  clear_has_forcesensordata();
}
inline const ::aubo::robot::common::ProtoForceSensorData& ProtoForceSensorDataResponse::forcesensordata() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoForceSensorDataResponse.forceSensorData)
  return forcesensordata_ != NULL ? *forcesensordata_ : *default_instance_->forcesensordata_;
}
inline ::aubo::robot::common::ProtoForceSensorData* ProtoForceSensorDataResponse::mutable_forcesensordata() {
  set_has_forcesensordata();
  if (forcesensordata_ == NULL) forcesensordata_ = new ::aubo::robot::common::ProtoForceSensorData;
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ProtoForceSensorDataResponse.forceSensorData)
  return forcesensordata_;
}
inline ::aubo::robot::common::ProtoForceSensorData* ProtoForceSensorDataResponse::release_forcesensordata() {
  clear_has_forcesensordata();
  ::aubo::robot::common::ProtoForceSensorData* temp = forcesensordata_;
  forcesensordata_ = NULL;
  return temp;
}
inline void ProtoForceSensorDataResponse::set_allocated_forcesensordata(::aubo::robot::common::ProtoForceSensorData* forcesensordata) {
  delete forcesensordata_;
  forcesensordata_ = forcesensordata;
  if (forcesensordata) {
    set_has_forcesensordata();
  } else {
    clear_has_forcesensordata();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.ProtoForceSensorDataResponse.forceSensorData)
}

// repeated .aubo.robot.common.RobotCommonResponse errorInfo = 2;
inline int ProtoForceSensorDataResponse::errorinfo_size() const {
  return errorinfo_.size();
}
inline void ProtoForceSensorDataResponse::clear_errorinfo() {
  errorinfo_.Clear();
}
inline const ::aubo::robot::common::RobotCommonResponse& ProtoForceSensorDataResponse::errorinfo(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoForceSensorDataResponse.errorInfo)
  return errorinfo_.Get(index);
}
inline ::aubo::robot::common::RobotCommonResponse* ProtoForceSensorDataResponse::mutable_errorinfo(int index) {
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ProtoForceSensorDataResponse.errorInfo)
  return errorinfo_.Mutable(index);
}
inline ::aubo::robot::common::RobotCommonResponse* ProtoForceSensorDataResponse::add_errorinfo() {
  // @@protoc_insertion_point(field_add:aubo.robot.common.ProtoForceSensorDataResponse.errorInfo)
  return errorinfo_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::RobotCommonResponse >&
ProtoForceSensorDataResponse::errorinfo() const {
  // @@protoc_insertion_point(field_list:aubo.robot.common.ProtoForceSensorDataResponse.errorInfo)
  return errorinfo_;
}
inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::RobotCommonResponse >*
ProtoForceSensorDataResponse::mutable_errorinfo() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.common.ProtoForceSensorDataResponse.errorInfo)
  return &errorinfo_;
}

// -------------------------------------------------------------------

// ProtoJointStatus

// required int32 jointCurrentI = 1;
inline bool ProtoJointStatus::has_jointcurrenti() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoJointStatus::set_has_jointcurrenti() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoJointStatus::clear_has_jointcurrenti() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoJointStatus::clear_jointcurrenti() {
  jointcurrenti_ = 0;
  clear_has_jointcurrenti();
}
inline ::google::protobuf::int32 ProtoJointStatus::jointcurrenti() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoJointStatus.jointCurrentI)
  return jointcurrenti_;
}
inline void ProtoJointStatus::set_jointcurrenti(::google::protobuf::int32 value) {
  set_has_jointcurrenti();
  jointcurrenti_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoJointStatus.jointCurrentI)
}

// required int32 jointSpeedMoto = 2;
inline bool ProtoJointStatus::has_jointspeedmoto() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoJointStatus::set_has_jointspeedmoto() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoJointStatus::clear_has_jointspeedmoto() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoJointStatus::clear_jointspeedmoto() {
  jointspeedmoto_ = 0;
  clear_has_jointspeedmoto();
}
inline ::google::protobuf::int32 ProtoJointStatus::jointspeedmoto() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoJointStatus.jointSpeedMoto)
  return jointspeedmoto_;
}
inline void ProtoJointStatus::set_jointspeedmoto(::google::protobuf::int32 value) {
  set_has_jointspeedmoto();
  jointspeedmoto_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoJointStatus.jointSpeedMoto)
}

// required float jointPosJ = 3;
inline bool ProtoJointStatus::has_jointposj() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void ProtoJointStatus::set_has_jointposj() {
  _has_bits_[0] |= 0x00000004u;
}
inline void ProtoJointStatus::clear_has_jointposj() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void ProtoJointStatus::clear_jointposj() {
  jointposj_ = 0;
  clear_has_jointposj();
}
inline float ProtoJointStatus::jointposj() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoJointStatus.jointPosJ)
  return jointposj_;
}
inline void ProtoJointStatus::set_jointposj(float value) {
  set_has_jointposj();
  jointposj_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoJointStatus.jointPosJ)
}

// required float jointCurVol = 4;
inline bool ProtoJointStatus::has_jointcurvol() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void ProtoJointStatus::set_has_jointcurvol() {
  _has_bits_[0] |= 0x00000008u;
}
inline void ProtoJointStatus::clear_has_jointcurvol() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void ProtoJointStatus::clear_jointcurvol() {
  jointcurvol_ = 0;
  clear_has_jointcurvol();
}
inline float ProtoJointStatus::jointcurvol() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoJointStatus.jointCurVol)
  return jointcurvol_;
}
inline void ProtoJointStatus::set_jointcurvol(float value) {
  set_has_jointcurvol();
  jointcurvol_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoJointStatus.jointCurVol)
}

// required float jointCurTemp = 5;
inline bool ProtoJointStatus::has_jointcurtemp() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void ProtoJointStatus::set_has_jointcurtemp() {
  _has_bits_[0] |= 0x00000010u;
}
inline void ProtoJointStatus::clear_has_jointcurtemp() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void ProtoJointStatus::clear_jointcurtemp() {
  jointcurtemp_ = 0;
  clear_has_jointcurtemp();
}
inline float ProtoJointStatus::jointcurtemp() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoJointStatus.jointCurTemp)
  return jointcurtemp_;
}
inline void ProtoJointStatus::set_jointcurtemp(float value) {
  set_has_jointcurtemp();
  jointcurtemp_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoJointStatus.jointCurTemp)
}

// required int32 jointTagCurrentI = 6;
inline bool ProtoJointStatus::has_jointtagcurrenti() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void ProtoJointStatus::set_has_jointtagcurrenti() {
  _has_bits_[0] |= 0x00000020u;
}
inline void ProtoJointStatus::clear_has_jointtagcurrenti() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void ProtoJointStatus::clear_jointtagcurrenti() {
  jointtagcurrenti_ = 0;
  clear_has_jointtagcurrenti();
}
inline ::google::protobuf::int32 ProtoJointStatus::jointtagcurrenti() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoJointStatus.jointTagCurrentI)
  return jointtagcurrenti_;
}
inline void ProtoJointStatus::set_jointtagcurrenti(::google::protobuf::int32 value) {
  set_has_jointtagcurrenti();
  jointtagcurrenti_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoJointStatus.jointTagCurrentI)
}

// required float jointTagSpeedMoto = 7;
inline bool ProtoJointStatus::has_jointtagspeedmoto() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void ProtoJointStatus::set_has_jointtagspeedmoto() {
  _has_bits_[0] |= 0x00000040u;
}
inline void ProtoJointStatus::clear_has_jointtagspeedmoto() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void ProtoJointStatus::clear_jointtagspeedmoto() {
  jointtagspeedmoto_ = 0;
  clear_has_jointtagspeedmoto();
}
inline float ProtoJointStatus::jointtagspeedmoto() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoJointStatus.jointTagSpeedMoto)
  return jointtagspeedmoto_;
}
inline void ProtoJointStatus::set_jointtagspeedmoto(float value) {
  set_has_jointtagspeedmoto();
  jointtagspeedmoto_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoJointStatus.jointTagSpeedMoto)
}

// required float jointTagPosJ = 8;
inline bool ProtoJointStatus::has_jointtagposj() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void ProtoJointStatus::set_has_jointtagposj() {
  _has_bits_[0] |= 0x00000080u;
}
inline void ProtoJointStatus::clear_has_jointtagposj() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void ProtoJointStatus::clear_jointtagposj() {
  jointtagposj_ = 0;
  clear_has_jointtagposj();
}
inline float ProtoJointStatus::jointtagposj() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoJointStatus.jointTagPosJ)
  return jointtagposj_;
}
inline void ProtoJointStatus::set_jointtagposj(float value) {
  set_has_jointtagposj();
  jointtagposj_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoJointStatus.jointTagPosJ)
}

// required uint32 jointErrorNum = 9;
inline bool ProtoJointStatus::has_jointerrornum() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
inline void ProtoJointStatus::set_has_jointerrornum() {
  _has_bits_[0] |= 0x00000100u;
}
inline void ProtoJointStatus::clear_has_jointerrornum() {
  _has_bits_[0] &= ~0x00000100u;
}
inline void ProtoJointStatus::clear_jointerrornum() {
  jointerrornum_ = 0u;
  clear_has_jointerrornum();
}
inline ::google::protobuf::uint32 ProtoJointStatus::jointerrornum() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoJointStatus.jointErrorNum)
  return jointerrornum_;
}
inline void ProtoJointStatus::set_jointerrornum(::google::protobuf::uint32 value) {
  set_has_jointerrornum();
  jointerrornum_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoJointStatus.jointErrorNum)
}

// -------------------------------------------------------------------

// ProtoRobotAllJointStatusResponse

// required uint32 jointCount = 1;
inline bool ProtoRobotAllJointStatusResponse::has_jointcount() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoRobotAllJointStatusResponse::set_has_jointcount() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoRobotAllJointStatusResponse::clear_has_jointcount() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoRobotAllJointStatusResponse::clear_jointcount() {
  jointcount_ = 0u;
  clear_has_jointcount();
}
inline ::google::protobuf::uint32 ProtoRobotAllJointStatusResponse::jointcount() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoRobotAllJointStatusResponse.jointCount)
  return jointcount_;
}
inline void ProtoRobotAllJointStatusResponse::set_jointcount(::google::protobuf::uint32 value) {
  set_has_jointcount();
  jointcount_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoRobotAllJointStatusResponse.jointCount)
}

// repeated .aubo.robot.common.ProtoJointStatus jointStatus = 2;
inline int ProtoRobotAllJointStatusResponse::jointstatus_size() const {
  return jointstatus_.size();
}
inline void ProtoRobotAllJointStatusResponse::clear_jointstatus() {
  jointstatus_.Clear();
}
inline const ::aubo::robot::common::ProtoJointStatus& ProtoRobotAllJointStatusResponse::jointstatus(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoRobotAllJointStatusResponse.jointStatus)
  return jointstatus_.Get(index);
}
inline ::aubo::robot::common::ProtoJointStatus* ProtoRobotAllJointStatusResponse::mutable_jointstatus(int index) {
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ProtoRobotAllJointStatusResponse.jointStatus)
  return jointstatus_.Mutable(index);
}
inline ::aubo::robot::common::ProtoJointStatus* ProtoRobotAllJointStatusResponse::add_jointstatus() {
  // @@protoc_insertion_point(field_add:aubo.robot.common.ProtoRobotAllJointStatusResponse.jointStatus)
  return jointstatus_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ProtoJointStatus >&
ProtoRobotAllJointStatusResponse::jointstatus() const {
  // @@protoc_insertion_point(field_list:aubo.robot.common.ProtoRobotAllJointStatusResponse.jointStatus)
  return jointstatus_;
}
inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ProtoJointStatus >*
ProtoRobotAllJointStatusResponse::mutable_jointstatus() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.common.ProtoRobotAllJointStatusResponse.jointStatus)
  return &jointstatus_;
}

// repeated .aubo.robot.common.RobotCommonResponse errorInfo = 3;
inline int ProtoRobotAllJointStatusResponse::errorinfo_size() const {
  return errorinfo_.size();
}
inline void ProtoRobotAllJointStatusResponse::clear_errorinfo() {
  errorinfo_.Clear();
}
inline const ::aubo::robot::common::RobotCommonResponse& ProtoRobotAllJointStatusResponse::errorinfo(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoRobotAllJointStatusResponse.errorInfo)
  return errorinfo_.Get(index);
}
inline ::aubo::robot::common::RobotCommonResponse* ProtoRobotAllJointStatusResponse::mutable_errorinfo(int index) {
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ProtoRobotAllJointStatusResponse.errorInfo)
  return errorinfo_.Mutable(index);
}
inline ::aubo::robot::common::RobotCommonResponse* ProtoRobotAllJointStatusResponse::add_errorinfo() {
  // @@protoc_insertion_point(field_add:aubo.robot.common.ProtoRobotAllJointStatusResponse.errorInfo)
  return errorinfo_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::RobotCommonResponse >&
ProtoRobotAllJointStatusResponse::errorinfo() const {
  // @@protoc_insertion_point(field_list:aubo.robot.common.ProtoRobotAllJointStatusResponse.errorInfo)
  return errorinfo_;
}
inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::RobotCommonResponse >*
ProtoRobotAllJointStatusResponse::mutable_errorinfo() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.common.ProtoRobotAllJointStatusResponse.errorInfo)
  return &errorinfo_;
}

// -------------------------------------------------------------------

// relativeMove_t

// required bool ena = 1;
inline bool relativeMove_t::has_ena() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void relativeMove_t::set_has_ena() {
  _has_bits_[0] |= 0x00000001u;
}
inline void relativeMove_t::clear_has_ena() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void relativeMove_t::clear_ena() {
  ena_ = false;
  clear_has_ena();
}
inline bool relativeMove_t::ena() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.relativeMove_t.ena)
  return ena_;
}
inline void relativeMove_t::set_ena(bool value) {
  set_has_ena();
  ena_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.relativeMove_t.ena)
}

// repeated float relativePosition = 2;
inline int relativeMove_t::relativeposition_size() const {
  return relativeposition_.size();
}
inline void relativeMove_t::clear_relativeposition() {
  relativeposition_.Clear();
}
inline float relativeMove_t::relativeposition(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.relativeMove_t.relativePosition)
  return relativeposition_.Get(index);
}
inline void relativeMove_t::set_relativeposition(int index, float value) {
  relativeposition_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.common.relativeMove_t.relativePosition)
}
inline void relativeMove_t::add_relativeposition(float value) {
  relativeposition_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.common.relativeMove_t.relativePosition)
}
inline const ::google::protobuf::RepeatedField< float >&
relativeMove_t::relativeposition() const {
  // @@protoc_insertion_point(field_list:aubo.robot.common.relativeMove_t.relativePosition)
  return relativeposition_;
}
inline ::google::protobuf::RepeatedField< float >*
relativeMove_t::mutable_relativeposition() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.common.relativeMove_t.relativePosition)
  return &relativeposition_;
}

// required .aubo.robot.common.cartesianOri_U relativeOrientation = 3;
inline bool relativeMove_t::has_relativeorientation() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void relativeMove_t::set_has_relativeorientation() {
  _has_bits_[0] |= 0x00000004u;
}
inline void relativeMove_t::clear_has_relativeorientation() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void relativeMove_t::clear_relativeorientation() {
  if (relativeorientation_ != NULL) relativeorientation_->::aubo::robot::common::cartesianOri_U::Clear();
  clear_has_relativeorientation();
}
inline const ::aubo::robot::common::cartesianOri_U& relativeMove_t::relativeorientation() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.relativeMove_t.relativeOrientation)
  return relativeorientation_ != NULL ? *relativeorientation_ : *default_instance_->relativeorientation_;
}
inline ::aubo::robot::common::cartesianOri_U* relativeMove_t::mutable_relativeorientation() {
  set_has_relativeorientation();
  if (relativeorientation_ == NULL) relativeorientation_ = new ::aubo::robot::common::cartesianOri_U;
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.relativeMove_t.relativeOrientation)
  return relativeorientation_;
}
inline ::aubo::robot::common::cartesianOri_U* relativeMove_t::release_relativeorientation() {
  clear_has_relativeorientation();
  ::aubo::robot::common::cartesianOri_U* temp = relativeorientation_;
  relativeorientation_ = NULL;
  return temp;
}
inline void relativeMove_t::set_allocated_relativeorientation(::aubo::robot::common::cartesianOri_U* relativeorientation) {
  delete relativeorientation_;
  relativeorientation_ = relativeorientation;
  if (relativeorientation) {
    set_has_relativeorientation();
  } else {
    clear_has_relativeorientation();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.relativeMove_t.relativeOrientation)
}

// -------------------------------------------------------------------

// joint_cart_U

// repeated double cartPara = 1;
inline int joint_cart_U::cartpara_size() const {
  return cartpara_.size();
}
inline void joint_cart_U::clear_cartpara() {
  cartpara_.Clear();
}
inline double joint_cart_U::cartpara(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.joint_cart_U.cartPara)
  return cartpara_.Get(index);
}
inline void joint_cart_U::set_cartpara(int index, double value) {
  cartpara_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.common.joint_cart_U.cartPara)
}
inline void joint_cart_U::add_cartpara(double value) {
  cartpara_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.common.joint_cart_U.cartPara)
}
inline const ::google::protobuf::RepeatedField< double >&
joint_cart_U::cartpara() const {
  // @@protoc_insertion_point(field_list:aubo.robot.common.joint_cart_U.cartPara)
  return cartpara_;
}
inline ::google::protobuf::RepeatedField< double >*
joint_cart_U::mutable_cartpara() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.common.joint_cart_U.cartPara)
  return &cartpara_;
}

// repeated double jointPara = 2;
inline int joint_cart_U::jointpara_size() const {
  return jointpara_.size();
}
inline void joint_cart_U::clear_jointpara() {
  jointpara_.Clear();
}
inline double joint_cart_U::jointpara(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.joint_cart_U.jointPara)
  return jointpara_.Get(index);
}
inline void joint_cart_U::set_jointpara(int index, double value) {
  jointpara_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.common.joint_cart_U.jointPara)
}
inline void joint_cart_U::add_jointpara(double value) {
  jointpara_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.common.joint_cart_U.jointPara)
}
inline const ::google::protobuf::RepeatedField< double >&
joint_cart_U::jointpara() const {
  // @@protoc_insertion_point(field_list:aubo.robot.common.joint_cart_U.jointPara)
  return jointpara_;
}
inline ::google::protobuf::RepeatedField< double >*
joint_cart_U::mutable_jointpara() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.common.joint_cart_U.jointPara)
  return &jointpara_;
}

// -------------------------------------------------------------------

// arrivalAhead_t

// required int32 arrivalAheadStat = 1;
inline bool arrivalAhead_t::has_arrivalaheadstat() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void arrivalAhead_t::set_has_arrivalaheadstat() {
  _has_bits_[0] |= 0x00000001u;
}
inline void arrivalAhead_t::clear_has_arrivalaheadstat() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void arrivalAhead_t::clear_arrivalaheadstat() {
  arrivalaheadstat_ = 0;
  clear_has_arrivalaheadstat();
}
inline ::google::protobuf::int32 arrivalAhead_t::arrivalaheadstat() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.arrivalAhead_t.arrivalAheadStat)
  return arrivalaheadstat_;
}
inline void arrivalAhead_t::set_arrivalaheadstat(::google::protobuf::int32 value) {
  set_has_arrivalaheadstat();
  arrivalaheadstat_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.arrivalAhead_t.arrivalAheadStat)
}

// required double arrivalAheadThr = 2;
inline bool arrivalAhead_t::has_arrivalaheadthr() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void arrivalAhead_t::set_has_arrivalaheadthr() {
  _has_bits_[0] |= 0x00000002u;
}
inline void arrivalAhead_t::clear_has_arrivalaheadthr() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void arrivalAhead_t::clear_arrivalaheadthr() {
  arrivalaheadthr_ = 0;
  clear_has_arrivalaheadthr();
}
inline double arrivalAhead_t::arrivalaheadthr() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.arrivalAhead_t.arrivalAheadThr)
  return arrivalaheadthr_;
}
inline void arrivalAhead_t::set_arrivalaheadthr(double value) {
  set_has_arrivalaheadthr();
  arrivalaheadthr_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.arrivalAhead_t.arrivalAheadThr)
}

// -------------------------------------------------------------------

// ProtoToolInEndDesc

// required .aubo.robot.common.Pos toolInEndPosition = 1;
inline bool ProtoToolInEndDesc::has_toolinendposition() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoToolInEndDesc::set_has_toolinendposition() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoToolInEndDesc::clear_has_toolinendposition() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoToolInEndDesc::clear_toolinendposition() {
  if (toolinendposition_ != NULL) toolinendposition_->::aubo::robot::common::Pos::Clear();
  clear_has_toolinendposition();
}
inline const ::aubo::robot::common::Pos& ProtoToolInEndDesc::toolinendposition() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoToolInEndDesc.toolInEndPosition)
  return toolinendposition_ != NULL ? *toolinendposition_ : *default_instance_->toolinendposition_;
}
inline ::aubo::robot::common::Pos* ProtoToolInEndDesc::mutable_toolinendposition() {
  set_has_toolinendposition();
  if (toolinendposition_ == NULL) toolinendposition_ = new ::aubo::robot::common::Pos;
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ProtoToolInEndDesc.toolInEndPosition)
  return toolinendposition_;
}
inline ::aubo::robot::common::Pos* ProtoToolInEndDesc::release_toolinendposition() {
  clear_has_toolinendposition();
  ::aubo::robot::common::Pos* temp = toolinendposition_;
  toolinendposition_ = NULL;
  return temp;
}
inline void ProtoToolInEndDesc::set_allocated_toolinendposition(::aubo::robot::common::Pos* toolinendposition) {
  delete toolinendposition_;
  toolinendposition_ = toolinendposition;
  if (toolinendposition) {
    set_has_toolinendposition();
  } else {
    clear_has_toolinendposition();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.ProtoToolInEndDesc.toolInEndPosition)
}

// required .aubo.robot.common.Ori toolInEndOrientation = 2;
inline bool ProtoToolInEndDesc::has_toolinendorientation() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoToolInEndDesc::set_has_toolinendorientation() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoToolInEndDesc::clear_has_toolinendorientation() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoToolInEndDesc::clear_toolinendorientation() {
  if (toolinendorientation_ != NULL) toolinendorientation_->::aubo::robot::common::Ori::Clear();
  clear_has_toolinendorientation();
}
inline const ::aubo::robot::common::Ori& ProtoToolInEndDesc::toolinendorientation() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoToolInEndDesc.toolInEndOrientation)
  return toolinendorientation_ != NULL ? *toolinendorientation_ : *default_instance_->toolinendorientation_;
}
inline ::aubo::robot::common::Ori* ProtoToolInEndDesc::mutable_toolinendorientation() {
  set_has_toolinendorientation();
  if (toolinendorientation_ == NULL) toolinendorientation_ = new ::aubo::robot::common::Ori;
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ProtoToolInEndDesc.toolInEndOrientation)
  return toolinendorientation_;
}
inline ::aubo::robot::common::Ori* ProtoToolInEndDesc::release_toolinendorientation() {
  clear_has_toolinendorientation();
  ::aubo::robot::common::Ori* temp = toolinendorientation_;
  toolinendorientation_ = NULL;
  return temp;
}
inline void ProtoToolInEndDesc::set_allocated_toolinendorientation(::aubo::robot::common::Ori* toolinendorientation) {
  delete toolinendorientation_;
  toolinendorientation_ = toolinendorientation;
  if (toolinendorientation) {
    set_has_toolinendorientation();
  } else {
    clear_has_toolinendorientation();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.ProtoToolInEndDesc.toolInEndOrientation)
}

// -------------------------------------------------------------------

// RobotMoveProfile

// required .aubo.robot.movecondition.move_mode moveMode = 1;
inline bool RobotMoveProfile::has_movemode() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void RobotMoveProfile::set_has_movemode() {
  _has_bits_[0] |= 0x00000001u;
}
inline void RobotMoveProfile::clear_has_movemode() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void RobotMoveProfile::clear_movemode() {
  movemode_ = 0;
  clear_has_movemode();
}
inline ::aubo::robot::movecondition::move_mode RobotMoveProfile::movemode() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.RobotMoveProfile.moveMode)
  return static_cast< ::aubo::robot::movecondition::move_mode >(movemode_);
}
inline void RobotMoveProfile::set_movemode(::aubo::robot::movecondition::move_mode value) {
  assert(::aubo::robot::movecondition::move_mode_IsValid(value));
  set_has_movemode();
  movemode_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.RobotMoveProfile.moveMode)
}

// required .aubo.robot.movecondition.move_track subMoveMode = 2;
inline bool RobotMoveProfile::has_submovemode() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void RobotMoveProfile::set_has_submovemode() {
  _has_bits_[0] |= 0x00000002u;
}
inline void RobotMoveProfile::clear_has_submovemode() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void RobotMoveProfile::clear_submovemode() {
  submovemode_ = 0;
  clear_has_submovemode();
}
inline ::aubo::robot::movecondition::move_track RobotMoveProfile::submovemode() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.RobotMoveProfile.subMoveMode)
  return static_cast< ::aubo::robot::movecondition::move_track >(submovemode_);
}
inline void RobotMoveProfile::set_submovemode(::aubo::robot::movecondition::move_track value) {
  assert(::aubo::robot::movecondition::move_track_IsValid(value));
  set_has_submovemode();
  submovemode_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.RobotMoveProfile.subMoveMode)
}

// required .aubo.robot.movecondition.teach_mode teachMode = 3;
inline bool RobotMoveProfile::has_teachmode() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void RobotMoveProfile::set_has_teachmode() {
  _has_bits_[0] |= 0x00000004u;
}
inline void RobotMoveProfile::clear_has_teachmode() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void RobotMoveProfile::clear_teachmode() {
  teachmode_ = 0;
  clear_has_teachmode();
}
inline ::aubo::robot::movecondition::teach_mode RobotMoveProfile::teachmode() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.RobotMoveProfile.teachMode)
  return static_cast< ::aubo::robot::movecondition::teach_mode >(teachmode_);
}
inline void RobotMoveProfile::set_teachmode(::aubo::robot::movecondition::teach_mode value) {
  assert(::aubo::robot::movecondition::teach_mode_IsValid(value));
  set_has_teachmode();
  teachmode_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.RobotMoveProfile.teachMode)
}

// required bool enableIterIk = 4;
inline bool RobotMoveProfile::has_enableiterik() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void RobotMoveProfile::set_has_enableiterik() {
  _has_bits_[0] |= 0x00000008u;
}
inline void RobotMoveProfile::clear_has_enableiterik() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void RobotMoveProfile::clear_enableiterik() {
  enableiterik_ = false;
  clear_has_enableiterik();
}
inline bool RobotMoveProfile::enableiterik() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.RobotMoveProfile.enableIterIk)
  return enableiterik_;
}
inline void RobotMoveProfile::set_enableiterik(bool value) {
  set_has_enableiterik();
  enableiterik_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.RobotMoveProfile.enableIterIk)
}

// required bool toolTrack = 5;
inline bool RobotMoveProfile::has_tooltrack() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void RobotMoveProfile::set_has_tooltrack() {
  _has_bits_[0] |= 0x00000010u;
}
inline void RobotMoveProfile::clear_has_tooltrack() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void RobotMoveProfile::clear_tooltrack() {
  tooltrack_ = false;
  clear_has_tooltrack();
}
inline bool RobotMoveProfile::tooltrack() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.RobotMoveProfile.toolTrack)
  return tooltrack_;
}
inline void RobotMoveProfile::set_tooltrack(bool value) {
  set_has_tooltrack();
  tooltrack_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.RobotMoveProfile.toolTrack)
}

// required .aubo.robot.common.Pos toolInEndPosition = 6;
inline bool RobotMoveProfile::has_toolinendposition() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void RobotMoveProfile::set_has_toolinendposition() {
  _has_bits_[0] |= 0x00000020u;
}
inline void RobotMoveProfile::clear_has_toolinendposition() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void RobotMoveProfile::clear_toolinendposition() {
  if (toolinendposition_ != NULL) toolinendposition_->::aubo::robot::common::Pos::Clear();
  clear_has_toolinendposition();
}
inline const ::aubo::robot::common::Pos& RobotMoveProfile::toolinendposition() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.RobotMoveProfile.toolInEndPosition)
  return toolinendposition_ != NULL ? *toolinendposition_ : *default_instance_->toolinendposition_;
}
inline ::aubo::robot::common::Pos* RobotMoveProfile::mutable_toolinendposition() {
  set_has_toolinendposition();
  if (toolinendposition_ == NULL) toolinendposition_ = new ::aubo::robot::common::Pos;
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.RobotMoveProfile.toolInEndPosition)
  return toolinendposition_;
}
inline ::aubo::robot::common::Pos* RobotMoveProfile::release_toolinendposition() {
  clear_has_toolinendposition();
  ::aubo::robot::common::Pos* temp = toolinendposition_;
  toolinendposition_ = NULL;
  return temp;
}
inline void RobotMoveProfile::set_allocated_toolinendposition(::aubo::robot::common::Pos* toolinendposition) {
  delete toolinendposition_;
  toolinendposition_ = toolinendposition;
  if (toolinendposition) {
    set_has_toolinendposition();
  } else {
    clear_has_toolinendposition();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.RobotMoveProfile.toolInEndPosition)
}

// required .aubo.robot.common.Ori toolInEndOrientation = 7;
inline bool RobotMoveProfile::has_toolinendorientation() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void RobotMoveProfile::set_has_toolinendorientation() {
  _has_bits_[0] |= 0x00000040u;
}
inline void RobotMoveProfile::clear_has_toolinendorientation() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void RobotMoveProfile::clear_toolinendorientation() {
  if (toolinendorientation_ != NULL) toolinendorientation_->::aubo::robot::common::Ori::Clear();
  clear_has_toolinendorientation();
}
inline const ::aubo::robot::common::Ori& RobotMoveProfile::toolinendorientation() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.RobotMoveProfile.toolInEndOrientation)
  return toolinendorientation_ != NULL ? *toolinendorientation_ : *default_instance_->toolinendorientation_;
}
inline ::aubo::robot::common::Ori* RobotMoveProfile::mutable_toolinendorientation() {
  set_has_toolinendorientation();
  if (toolinendorientation_ == NULL) toolinendorientation_ = new ::aubo::robot::common::Ori;
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.RobotMoveProfile.toolInEndOrientation)
  return toolinendorientation_;
}
inline ::aubo::robot::common::Ori* RobotMoveProfile::release_toolinendorientation() {
  clear_has_toolinendorientation();
  ::aubo::robot::common::Ori* temp = toolinendorientation_;
  toolinendorientation_ = NULL;
  return temp;
}
inline void RobotMoveProfile::set_allocated_toolinendorientation(::aubo::robot::common::Ori* toolinendorientation) {
  delete toolinendorientation_;
  toolinendorientation_ = toolinendorientation;
  if (toolinendorientation) {
    set_has_toolinendorientation();
  } else {
    clear_has_toolinendorientation();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.RobotMoveProfile.toolInEndOrientation)
}

// required .aubo.robot.common.relativeMove_t relativeMove = 8;
inline bool RobotMoveProfile::has_relativemove() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void RobotMoveProfile::set_has_relativemove() {
  _has_bits_[0] |= 0x00000080u;
}
inline void RobotMoveProfile::clear_has_relativemove() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void RobotMoveProfile::clear_relativemove() {
  if (relativemove_ != NULL) relativemove_->::aubo::robot::common::relativeMove_t::Clear();
  clear_has_relativemove();
}
inline const ::aubo::robot::common::relativeMove_t& RobotMoveProfile::relativemove() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.RobotMoveProfile.relativeMove)
  return relativemove_ != NULL ? *relativemove_ : *default_instance_->relativemove_;
}
inline ::aubo::robot::common::relativeMove_t* RobotMoveProfile::mutable_relativemove() {
  set_has_relativemove();
  if (relativemove_ == NULL) relativemove_ = new ::aubo::robot::common::relativeMove_t;
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.RobotMoveProfile.relativeMove)
  return relativemove_;
}
inline ::aubo::robot::common::relativeMove_t* RobotMoveProfile::release_relativemove() {
  clear_has_relativemove();
  ::aubo::robot::common::relativeMove_t* temp = relativemove_;
  relativemove_ = NULL;
  return temp;
}
inline void RobotMoveProfile::set_allocated_relativemove(::aubo::robot::common::relativeMove_t* relativemove) {
  delete relativemove_;
  relativemove_ = relativemove;
  if (relativemove) {
    set_has_relativemove();
  } else {
    clear_has_relativemove();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.RobotMoveProfile.relativeMove)
}

// required .aubo.robot.common.joint_cart_U maxVelc = 9;
inline bool RobotMoveProfile::has_maxvelc() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
inline void RobotMoveProfile::set_has_maxvelc() {
  _has_bits_[0] |= 0x00000100u;
}
inline void RobotMoveProfile::clear_has_maxvelc() {
  _has_bits_[0] &= ~0x00000100u;
}
inline void RobotMoveProfile::clear_maxvelc() {
  if (maxvelc_ != NULL) maxvelc_->::aubo::robot::common::joint_cart_U::Clear();
  clear_has_maxvelc();
}
inline const ::aubo::robot::common::joint_cart_U& RobotMoveProfile::maxvelc() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.RobotMoveProfile.maxVelc)
  return maxvelc_ != NULL ? *maxvelc_ : *default_instance_->maxvelc_;
}
inline ::aubo::robot::common::joint_cart_U* RobotMoveProfile::mutable_maxvelc() {
  set_has_maxvelc();
  if (maxvelc_ == NULL) maxvelc_ = new ::aubo::robot::common::joint_cart_U;
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.RobotMoveProfile.maxVelc)
  return maxvelc_;
}
inline ::aubo::robot::common::joint_cart_U* RobotMoveProfile::release_maxvelc() {
  clear_has_maxvelc();
  ::aubo::robot::common::joint_cart_U* temp = maxvelc_;
  maxvelc_ = NULL;
  return temp;
}
inline void RobotMoveProfile::set_allocated_maxvelc(::aubo::robot::common::joint_cart_U* maxvelc) {
  delete maxvelc_;
  maxvelc_ = maxvelc;
  if (maxvelc) {
    set_has_maxvelc();
  } else {
    clear_has_maxvelc();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.RobotMoveProfile.maxVelc)
}

// required .aubo.robot.common.joint_cart_U maxAcc = 10;
inline bool RobotMoveProfile::has_maxacc() const {
  return (_has_bits_[0] & 0x00000200u) != 0;
}
inline void RobotMoveProfile::set_has_maxacc() {
  _has_bits_[0] |= 0x00000200u;
}
inline void RobotMoveProfile::clear_has_maxacc() {
  _has_bits_[0] &= ~0x00000200u;
}
inline void RobotMoveProfile::clear_maxacc() {
  if (maxacc_ != NULL) maxacc_->::aubo::robot::common::joint_cart_U::Clear();
  clear_has_maxacc();
}
inline const ::aubo::robot::common::joint_cart_U& RobotMoveProfile::maxacc() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.RobotMoveProfile.maxAcc)
  return maxacc_ != NULL ? *maxacc_ : *default_instance_->maxacc_;
}
inline ::aubo::robot::common::joint_cart_U* RobotMoveProfile::mutable_maxacc() {
  set_has_maxacc();
  if (maxacc_ == NULL) maxacc_ = new ::aubo::robot::common::joint_cart_U;
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.RobotMoveProfile.maxAcc)
  return maxacc_;
}
inline ::aubo::robot::common::joint_cart_U* RobotMoveProfile::release_maxacc() {
  clear_has_maxacc();
  ::aubo::robot::common::joint_cart_U* temp = maxacc_;
  maxacc_ = NULL;
  return temp;
}
inline void RobotMoveProfile::set_allocated_maxacc(::aubo::robot::common::joint_cart_U* maxacc) {
  delete maxacc_;
  maxacc_ = maxacc;
  if (maxacc) {
    set_has_maxacc();
  } else {
    clear_has_maxacc();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.RobotMoveProfile.maxAcc)
}

// required float blendRadius = 11;
inline bool RobotMoveProfile::has_blendradius() const {
  return (_has_bits_[0] & 0x00000400u) != 0;
}
inline void RobotMoveProfile::set_has_blendradius() {
  _has_bits_[0] |= 0x00000400u;
}
inline void RobotMoveProfile::clear_has_blendradius() {
  _has_bits_[0] &= ~0x00000400u;
}
inline void RobotMoveProfile::clear_blendradius() {
  blendradius_ = 0;
  clear_has_blendradius();
}
inline float RobotMoveProfile::blendradius() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.RobotMoveProfile.blendRadius)
  return blendradius_;
}
inline void RobotMoveProfile::set_blendradius(float value) {
  set_has_blendradius();
  blendradius_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.RobotMoveProfile.blendRadius)
}

// required int32 circularLoopTimes = 12;
inline bool RobotMoveProfile::has_circularlooptimes() const {
  return (_has_bits_[0] & 0x00000800u) != 0;
}
inline void RobotMoveProfile::set_has_circularlooptimes() {
  _has_bits_[0] |= 0x00000800u;
}
inline void RobotMoveProfile::clear_has_circularlooptimes() {
  _has_bits_[0] &= ~0x00000800u;
}
inline void RobotMoveProfile::clear_circularlooptimes() {
  circularlooptimes_ = 0;
  clear_has_circularlooptimes();
}
inline ::google::protobuf::int32 RobotMoveProfile::circularlooptimes() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.RobotMoveProfile.circularLoopTimes)
  return circularlooptimes_;
}
inline void RobotMoveProfile::set_circularlooptimes(::google::protobuf::int32 value) {
  set_has_circularlooptimes();
  circularlooptimes_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.RobotMoveProfile.circularLoopTimes)
}

// required .aubo.robot.common.arrivalAhead_t arrivalAhead = 13;
inline bool RobotMoveProfile::has_arrivalahead() const {
  return (_has_bits_[0] & 0x00001000u) != 0;
}
inline void RobotMoveProfile::set_has_arrivalahead() {
  _has_bits_[0] |= 0x00001000u;
}
inline void RobotMoveProfile::clear_has_arrivalahead() {
  _has_bits_[0] &= ~0x00001000u;
}
inline void RobotMoveProfile::clear_arrivalahead() {
  if (arrivalahead_ != NULL) arrivalahead_->::aubo::robot::common::arrivalAhead_t::Clear();
  clear_has_arrivalahead();
}
inline const ::aubo::robot::common::arrivalAhead_t& RobotMoveProfile::arrivalahead() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.RobotMoveProfile.arrivalAhead)
  return arrivalahead_ != NULL ? *arrivalahead_ : *default_instance_->arrivalahead_;
}
inline ::aubo::robot::common::arrivalAhead_t* RobotMoveProfile::mutable_arrivalahead() {
  set_has_arrivalahead();
  if (arrivalahead_ == NULL) arrivalahead_ = new ::aubo::robot::common::arrivalAhead_t;
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.RobotMoveProfile.arrivalAhead)
  return arrivalahead_;
}
inline ::aubo::robot::common::arrivalAhead_t* RobotMoveProfile::release_arrivalahead() {
  clear_has_arrivalahead();
  ::aubo::robot::common::arrivalAhead_t* temp = arrivalahead_;
  arrivalahead_ = NULL;
  return temp;
}
inline void RobotMoveProfile::set_allocated_arrivalahead(::aubo::robot::common::arrivalAhead_t* arrivalahead) {
  delete arrivalahead_;
  arrivalahead_ = arrivalahead;
  if (arrivalahead) {
    set_has_arrivalahead();
  } else {
    clear_has_arrivalahead();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.RobotMoveProfile.arrivalAhead)
}

// -------------------------------------------------------------------

// RobotMove

// required .aubo.robot.common.RobotMoveProfile move_profile = 1;
inline bool RobotMove::has_move_profile() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void RobotMove::set_has_move_profile() {
  _has_bits_[0] |= 0x00000001u;
}
inline void RobotMove::clear_has_move_profile() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void RobotMove::clear_move_profile() {
  if (move_profile_ != NULL) move_profile_->::aubo::robot::common::RobotMoveProfile::Clear();
  clear_has_move_profile();
}
inline const ::aubo::robot::common::RobotMoveProfile& RobotMove::move_profile() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.RobotMove.move_profile)
  return move_profile_ != NULL ? *move_profile_ : *default_instance_->move_profile_;
}
inline ::aubo::robot::common::RobotMoveProfile* RobotMove::mutable_move_profile() {
  set_has_move_profile();
  if (move_profile_ == NULL) move_profile_ = new ::aubo::robot::common::RobotMoveProfile;
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.RobotMove.move_profile)
  return move_profile_;
}
inline ::aubo::robot::common::RobotMoveProfile* RobotMove::release_move_profile() {
  clear_has_move_profile();
  ::aubo::robot::common::RobotMoveProfile* temp = move_profile_;
  move_profile_ = NULL;
  return temp;
}
inline void RobotMove::set_allocated_move_profile(::aubo::robot::common::RobotMoveProfile* move_profile) {
  delete move_profile_;
  move_profile_ = move_profile;
  if (move_profile) {
    set_has_move_profile();
  } else {
    clear_has_move_profile();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.RobotMove.move_profile)
}

// repeated .aubo.robot.common.ProtoRoadPoint roadPointVector = 2;
inline int RobotMove::roadpointvector_size() const {
  return roadpointvector_.size();
}
inline void RobotMove::clear_roadpointvector() {
  roadpointvector_.Clear();
}
inline const ::aubo::robot::common::ProtoRoadPoint& RobotMove::roadpointvector(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.RobotMove.roadPointVector)
  return roadpointvector_.Get(index);
}
inline ::aubo::robot::common::ProtoRoadPoint* RobotMove::mutable_roadpointvector(int index) {
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.RobotMove.roadPointVector)
  return roadpointvector_.Mutable(index);
}
inline ::aubo::robot::common::ProtoRoadPoint* RobotMove::add_roadpointvector() {
  // @@protoc_insertion_point(field_add:aubo.robot.common.RobotMove.roadPointVector)
  return roadpointvector_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ProtoRoadPoint >&
RobotMove::roadpointvector() const {
  // @@protoc_insertion_point(field_list:aubo.robot.common.RobotMove.roadPointVector)
  return roadpointvector_;
}
inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ProtoRoadPoint >*
RobotMove::mutable_roadpointvector() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.common.RobotMove.roadPointVector)
  return &roadpointvector_;
}

// -------------------------------------------------------------------

// RobotTeachMove

// required .aubo.robot.common.RobotMoveProfile move_profile = 1;
inline bool RobotTeachMove::has_move_profile() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void RobotTeachMove::set_has_move_profile() {
  _has_bits_[0] |= 0x00000001u;
}
inline void RobotTeachMove::clear_has_move_profile() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void RobotTeachMove::clear_move_profile() {
  if (move_profile_ != NULL) move_profile_->::aubo::robot::common::RobotMoveProfile::Clear();
  clear_has_move_profile();
}
inline const ::aubo::robot::common::RobotMoveProfile& RobotTeachMove::move_profile() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.RobotTeachMove.move_profile)
  return move_profile_ != NULL ? *move_profile_ : *default_instance_->move_profile_;
}
inline ::aubo::robot::common::RobotMoveProfile* RobotTeachMove::mutable_move_profile() {
  set_has_move_profile();
  if (move_profile_ == NULL) move_profile_ = new ::aubo::robot::common::RobotMoveProfile;
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.RobotTeachMove.move_profile)
  return move_profile_;
}
inline ::aubo::robot::common::RobotMoveProfile* RobotTeachMove::release_move_profile() {
  clear_has_move_profile();
  ::aubo::robot::common::RobotMoveProfile* temp = move_profile_;
  move_profile_ = NULL;
  return temp;
}
inline void RobotTeachMove::set_allocated_move_profile(::aubo::robot::common::RobotMoveProfile* move_profile) {
  delete move_profile_;
  move_profile_ = move_profile;
  if (move_profile) {
    set_has_move_profile();
  } else {
    clear_has_move_profile();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.RobotTeachMove.move_profile)
}

// required int32 coordinateSystemType = 2;
inline bool RobotTeachMove::has_coordinatesystemtype() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void RobotTeachMove::set_has_coordinatesystemtype() {
  _has_bits_[0] |= 0x00000002u;
}
inline void RobotTeachMove::clear_has_coordinatesystemtype() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void RobotTeachMove::clear_coordinatesystemtype() {
  coordinatesystemtype_ = 0;
  clear_has_coordinatesystemtype();
}
inline ::google::protobuf::int32 RobotTeachMove::coordinatesystemtype() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.RobotTeachMove.coordinateSystemType)
  return coordinatesystemtype_;
}
inline void RobotTeachMove::set_coordinatesystemtype(::google::protobuf::int32 value) {
  set_has_coordinatesystemtype();
  coordinatesystemtype_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.RobotTeachMove.coordinateSystemType)
}

// repeated .aubo.robot.common.ProtoRoadPoint calibrateRoadPointVector = 3;
inline int RobotTeachMove::calibrateroadpointvector_size() const {
  return calibrateroadpointvector_.size();
}
inline void RobotTeachMove::clear_calibrateroadpointvector() {
  calibrateroadpointvector_.Clear();
}
inline const ::aubo::robot::common::ProtoRoadPoint& RobotTeachMove::calibrateroadpointvector(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.RobotTeachMove.calibrateRoadPointVector)
  return calibrateroadpointvector_.Get(index);
}
inline ::aubo::robot::common::ProtoRoadPoint* RobotTeachMove::mutable_calibrateroadpointvector(int index) {
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.RobotTeachMove.calibrateRoadPointVector)
  return calibrateroadpointvector_.Mutable(index);
}
inline ::aubo::robot::common::ProtoRoadPoint* RobotTeachMove::add_calibrateroadpointvector() {
  // @@protoc_insertion_point(field_add:aubo.robot.common.RobotTeachMove.calibrateRoadPointVector)
  return calibrateroadpointvector_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ProtoRoadPoint >&
RobotTeachMove::calibrateroadpointvector() const {
  // @@protoc_insertion_point(field_list:aubo.robot.common.RobotTeachMove.calibrateRoadPointVector)
  return calibrateroadpointvector_;
}
inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ProtoRoadPoint >*
RobotTeachMove::mutable_calibrateroadpointvector() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.common.RobotTeachMove.calibrateRoadPointVector)
  return &calibrateroadpointvector_;
}

// repeated int32 calibrateMathod = 4;
inline int RobotTeachMove::calibratemathod_size() const {
  return calibratemathod_.size();
}
inline void RobotTeachMove::clear_calibratemathod() {
  calibratemathod_.Clear();
}
inline ::google::protobuf::int32 RobotTeachMove::calibratemathod(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.RobotTeachMove.calibrateMathod)
  return calibratemathod_.Get(index);
}
inline void RobotTeachMove::set_calibratemathod(int index, ::google::protobuf::int32 value) {
  calibratemathod_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.common.RobotTeachMove.calibrateMathod)
}
inline void RobotTeachMove::add_calibratemathod(::google::protobuf::int32 value) {
  calibratemathod_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.common.RobotTeachMove.calibrateMathod)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
RobotTeachMove::calibratemathod() const {
  // @@protoc_insertion_point(field_list:aubo.robot.common.RobotTeachMove.calibrateMathod)
  return calibratemathod_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
RobotTeachMove::mutable_calibratemathod() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.common.RobotTeachMove.calibrateMathod)
  return &calibratemathod_;
}

// repeated .aubo.robot.common.ProtoToolInEndDesc toolInEndDesc = 5;
inline int RobotTeachMove::toolinenddesc_size() const {
  return toolinenddesc_.size();
}
inline void RobotTeachMove::clear_toolinenddesc() {
  toolinenddesc_.Clear();
}
inline const ::aubo::robot::common::ProtoToolInEndDesc& RobotTeachMove::toolinenddesc(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.RobotTeachMove.toolInEndDesc)
  return toolinenddesc_.Get(index);
}
inline ::aubo::robot::common::ProtoToolInEndDesc* RobotTeachMove::mutable_toolinenddesc(int index) {
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.RobotTeachMove.toolInEndDesc)
  return toolinenddesc_.Mutable(index);
}
inline ::aubo::robot::common::ProtoToolInEndDesc* RobotTeachMove::add_toolinenddesc() {
  // @@protoc_insertion_point(field_add:aubo.robot.common.RobotTeachMove.toolInEndDesc)
  return toolinenddesc_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ProtoToolInEndDesc >&
RobotTeachMove::toolinenddesc() const {
  // @@protoc_insertion_point(field_list:aubo.robot.common.RobotTeachMove.toolInEndDesc)
  return toolinenddesc_;
}
inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ProtoToolInEndDesc >*
RobotTeachMove::mutable_toolinenddesc() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.common.RobotTeachMove.toolInEndDesc)
  return &toolinenddesc_;
}

// -------------------------------------------------------------------

// RobotCommonResponse

// required int32 errorCode = 1;
inline bool RobotCommonResponse::has_errorcode() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void RobotCommonResponse::set_has_errorcode() {
  _has_bits_[0] |= 0x00000001u;
}
inline void RobotCommonResponse::clear_has_errorcode() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void RobotCommonResponse::clear_errorcode() {
  errorcode_ = 0;
  clear_has_errorcode();
}
inline ::google::protobuf::int32 RobotCommonResponse::errorcode() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.RobotCommonResponse.errorCode)
  return errorcode_;
}
inline void RobotCommonResponse::set_errorcode(::google::protobuf::int32 value) {
  set_has_errorcode();
  errorcode_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.RobotCommonResponse.errorCode)
}

// required string errorMsg = 2;
inline bool RobotCommonResponse::has_errormsg() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void RobotCommonResponse::set_has_errormsg() {
  _has_bits_[0] |= 0x00000002u;
}
inline void RobotCommonResponse::clear_has_errormsg() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void RobotCommonResponse::clear_errormsg() {
  if (errormsg_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    errormsg_->clear();
  }
  clear_has_errormsg();
}
inline const ::std::string& RobotCommonResponse::errormsg() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.RobotCommonResponse.errorMsg)
  return *errormsg_;
}
inline void RobotCommonResponse::set_errormsg(const ::std::string& value) {
  set_has_errormsg();
  if (errormsg_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    errormsg_ = new ::std::string;
  }
  errormsg_->assign(value);
  // @@protoc_insertion_point(field_set:aubo.robot.common.RobotCommonResponse.errorMsg)
}
inline void RobotCommonResponse::set_errormsg(const char* value) {
  set_has_errormsg();
  if (errormsg_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    errormsg_ = new ::std::string;
  }
  errormsg_->assign(value);
  // @@protoc_insertion_point(field_set_char:aubo.robot.common.RobotCommonResponse.errorMsg)
}
inline void RobotCommonResponse::set_errormsg(const char* value, size_t size) {
  set_has_errormsg();
  if (errormsg_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    errormsg_ = new ::std::string;
  }
  errormsg_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:aubo.robot.common.RobotCommonResponse.errorMsg)
}
inline ::std::string* RobotCommonResponse::mutable_errormsg() {
  set_has_errormsg();
  if (errormsg_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    errormsg_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.RobotCommonResponse.errorMsg)
  return errormsg_;
}
inline ::std::string* RobotCommonResponse::release_errormsg() {
  clear_has_errormsg();
  if (errormsg_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = errormsg_;
    errormsg_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void RobotCommonResponse::set_allocated_errormsg(::std::string* errormsg) {
  if (errormsg_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete errormsg_;
  }
  if (errormsg) {
    set_has_errormsg();
    errormsg_ = errormsg;
  } else {
    clear_has_errormsg();
    errormsg_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.RobotCommonResponse.errorMsg)
}

// -------------------------------------------------------------------

// ProtoResponseRobotTcpParam

// repeated .aubo.robot.communication.RobotTcpParam tcpParam = 1;
inline int ProtoResponseRobotTcpParam::tcpparam_size() const {
  return tcpparam_.size();
}
inline void ProtoResponseRobotTcpParam::clear_tcpparam() {
  tcpparam_.Clear();
}
inline const ::aubo::robot::communication::RobotTcpParam& ProtoResponseRobotTcpParam::tcpparam(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoResponseRobotTcpParam.tcpParam)
  return tcpparam_.Get(index);
}
inline ::aubo::robot::communication::RobotTcpParam* ProtoResponseRobotTcpParam::mutable_tcpparam(int index) {
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ProtoResponseRobotTcpParam.tcpParam)
  return tcpparam_.Mutable(index);
}
inline ::aubo::robot::communication::RobotTcpParam* ProtoResponseRobotTcpParam::add_tcpparam() {
  // @@protoc_insertion_point(field_add:aubo.robot.common.ProtoResponseRobotTcpParam.tcpParam)
  return tcpparam_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::RobotTcpParam >&
ProtoResponseRobotTcpParam::tcpparam() const {
  // @@protoc_insertion_point(field_list:aubo.robot.common.ProtoResponseRobotTcpParam.tcpParam)
  return tcpparam_;
}
inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::RobotTcpParam >*
ProtoResponseRobotTcpParam::mutable_tcpparam() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.common.ProtoResponseRobotTcpParam.tcpParam)
  return &tcpparam_;
}

// required .aubo.robot.common.RobotCommonResponse errorInfo = 2;
inline bool ProtoResponseRobotTcpParam::has_errorinfo() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoResponseRobotTcpParam::set_has_errorinfo() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoResponseRobotTcpParam::clear_has_errorinfo() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoResponseRobotTcpParam::clear_errorinfo() {
  if (errorinfo_ != NULL) errorinfo_->::aubo::robot::common::RobotCommonResponse::Clear();
  clear_has_errorinfo();
}
inline const ::aubo::robot::common::RobotCommonResponse& ProtoResponseRobotTcpParam::errorinfo() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoResponseRobotTcpParam.errorInfo)
  return errorinfo_ != NULL ? *errorinfo_ : *default_instance_->errorinfo_;
}
inline ::aubo::robot::common::RobotCommonResponse* ProtoResponseRobotTcpParam::mutable_errorinfo() {
  set_has_errorinfo();
  if (errorinfo_ == NULL) errorinfo_ = new ::aubo::robot::common::RobotCommonResponse;
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ProtoResponseRobotTcpParam.errorInfo)
  return errorinfo_;
}
inline ::aubo::robot::common::RobotCommonResponse* ProtoResponseRobotTcpParam::release_errorinfo() {
  clear_has_errorinfo();
  ::aubo::robot::common::RobotCommonResponse* temp = errorinfo_;
  errorinfo_ = NULL;
  return temp;
}
inline void ProtoResponseRobotTcpParam::set_allocated_errorinfo(::aubo::robot::common::RobotCommonResponse* errorinfo) {
  delete errorinfo_;
  errorinfo_ = errorinfo;
  if (errorinfo) {
    set_has_errorinfo();
  } else {
    clear_has_errorinfo();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.ProtoResponseRobotTcpParam.errorInfo)
}

// -------------------------------------------------------------------

// ProtoResponseRobotGravityComponent

// repeated .aubo.robot.communication.RobotGravityComponent gravityComponent = 1;
inline int ProtoResponseRobotGravityComponent::gravitycomponent_size() const {
  return gravitycomponent_.size();
}
inline void ProtoResponseRobotGravityComponent::clear_gravitycomponent() {
  gravitycomponent_.Clear();
}
inline const ::aubo::robot::communication::RobotGravityComponent& ProtoResponseRobotGravityComponent::gravitycomponent(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoResponseRobotGravityComponent.gravityComponent)
  return gravitycomponent_.Get(index);
}
inline ::aubo::robot::communication::RobotGravityComponent* ProtoResponseRobotGravityComponent::mutable_gravitycomponent(int index) {
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ProtoResponseRobotGravityComponent.gravityComponent)
  return gravitycomponent_.Mutable(index);
}
inline ::aubo::robot::communication::RobotGravityComponent* ProtoResponseRobotGravityComponent::add_gravitycomponent() {
  // @@protoc_insertion_point(field_add:aubo.robot.common.ProtoResponseRobotGravityComponent.gravityComponent)
  return gravitycomponent_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::RobotGravityComponent >&
ProtoResponseRobotGravityComponent::gravitycomponent() const {
  // @@protoc_insertion_point(field_list:aubo.robot.common.ProtoResponseRobotGravityComponent.gravityComponent)
  return gravitycomponent_;
}
inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::RobotGravityComponent >*
ProtoResponseRobotGravityComponent::mutable_gravitycomponent() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.common.ProtoResponseRobotGravityComponent.gravityComponent)
  return &gravitycomponent_;
}

// required .aubo.robot.common.RobotCommonResponse errorInfo = 2;
inline bool ProtoResponseRobotGravityComponent::has_errorinfo() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoResponseRobotGravityComponent::set_has_errorinfo() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoResponseRobotGravityComponent::clear_has_errorinfo() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoResponseRobotGravityComponent::clear_errorinfo() {
  if (errorinfo_ != NULL) errorinfo_->::aubo::robot::common::RobotCommonResponse::Clear();
  clear_has_errorinfo();
}
inline const ::aubo::robot::common::RobotCommonResponse& ProtoResponseRobotGravityComponent::errorinfo() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoResponseRobotGravityComponent.errorInfo)
  return errorinfo_ != NULL ? *errorinfo_ : *default_instance_->errorinfo_;
}
inline ::aubo::robot::common::RobotCommonResponse* ProtoResponseRobotGravityComponent::mutable_errorinfo() {
  set_has_errorinfo();
  if (errorinfo_ == NULL) errorinfo_ = new ::aubo::robot::common::RobotCommonResponse;
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ProtoResponseRobotGravityComponent.errorInfo)
  return errorinfo_;
}
inline ::aubo::robot::common::RobotCommonResponse* ProtoResponseRobotGravityComponent::release_errorinfo() {
  clear_has_errorinfo();
  ::aubo::robot::common::RobotCommonResponse* temp = errorinfo_;
  errorinfo_ = NULL;
  return temp;
}
inline void ProtoResponseRobotGravityComponent::set_allocated_errorinfo(::aubo::robot::common::RobotCommonResponse* errorinfo) {
  delete errorinfo_;
  errorinfo_ = errorinfo;
  if (errorinfo) {
    set_has_errorinfo();
  } else {
    clear_has_errorinfo();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.ProtoResponseRobotGravityComponent.errorInfo)
}

// -------------------------------------------------------------------

// ProtoResponseRobotCollisionCurrent

// repeated .aubo.robot.communication.RobotCollisionCurrent collisionCurrent = 1;
inline int ProtoResponseRobotCollisionCurrent::collisioncurrent_size() const {
  return collisioncurrent_.size();
}
inline void ProtoResponseRobotCollisionCurrent::clear_collisioncurrent() {
  collisioncurrent_.Clear();
}
inline const ::aubo::robot::communication::RobotCollisionCurrent& ProtoResponseRobotCollisionCurrent::collisioncurrent(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoResponseRobotCollisionCurrent.collisionCurrent)
  return collisioncurrent_.Get(index);
}
inline ::aubo::robot::communication::RobotCollisionCurrent* ProtoResponseRobotCollisionCurrent::mutable_collisioncurrent(int index) {
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ProtoResponseRobotCollisionCurrent.collisionCurrent)
  return collisioncurrent_.Mutable(index);
}
inline ::aubo::robot::communication::RobotCollisionCurrent* ProtoResponseRobotCollisionCurrent::add_collisioncurrent() {
  // @@protoc_insertion_point(field_add:aubo.robot.common.ProtoResponseRobotCollisionCurrent.collisionCurrent)
  return collisioncurrent_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::RobotCollisionCurrent >&
ProtoResponseRobotCollisionCurrent::collisioncurrent() const {
  // @@protoc_insertion_point(field_list:aubo.robot.common.ProtoResponseRobotCollisionCurrent.collisionCurrent)
  return collisioncurrent_;
}
inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::RobotCollisionCurrent >*
ProtoResponseRobotCollisionCurrent::mutable_collisioncurrent() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.common.ProtoResponseRobotCollisionCurrent.collisionCurrent)
  return &collisioncurrent_;
}

// required .aubo.robot.common.RobotCommonResponse errorInfo = 2;
inline bool ProtoResponseRobotCollisionCurrent::has_errorinfo() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoResponseRobotCollisionCurrent::set_has_errorinfo() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoResponseRobotCollisionCurrent::clear_has_errorinfo() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoResponseRobotCollisionCurrent::clear_errorinfo() {
  if (errorinfo_ != NULL) errorinfo_->::aubo::robot::common::RobotCommonResponse::Clear();
  clear_has_errorinfo();
}
inline const ::aubo::robot::common::RobotCommonResponse& ProtoResponseRobotCollisionCurrent::errorinfo() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoResponseRobotCollisionCurrent.errorInfo)
  return errorinfo_ != NULL ? *errorinfo_ : *default_instance_->errorinfo_;
}
inline ::aubo::robot::common::RobotCommonResponse* ProtoResponseRobotCollisionCurrent::mutable_errorinfo() {
  set_has_errorinfo();
  if (errorinfo_ == NULL) errorinfo_ = new ::aubo::robot::common::RobotCommonResponse;
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ProtoResponseRobotCollisionCurrent.errorInfo)
  return errorinfo_;
}
inline ::aubo::robot::common::RobotCommonResponse* ProtoResponseRobotCollisionCurrent::release_errorinfo() {
  clear_has_errorinfo();
  ::aubo::robot::common::RobotCommonResponse* temp = errorinfo_;
  errorinfo_ = NULL;
  return temp;
}
inline void ProtoResponseRobotCollisionCurrent::set_allocated_errorinfo(::aubo::robot::common::RobotCommonResponse* errorinfo) {
  delete errorinfo_;
  errorinfo_ = errorinfo;
  if (errorinfo) {
    set_has_errorinfo();
  } else {
    clear_has_errorinfo();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.ProtoResponseRobotCollisionCurrent.errorInfo)
}

// -------------------------------------------------------------------

// ProtoResponseRobotDevInfo

// repeated .aubo.robot.communication.OurRobotDevInfo devInfo = 1;
inline int ProtoResponseRobotDevInfo::devinfo_size() const {
  return devinfo_.size();
}
inline void ProtoResponseRobotDevInfo::clear_devinfo() {
  devinfo_.Clear();
}
inline const ::aubo::robot::communication::OurRobotDevInfo& ProtoResponseRobotDevInfo::devinfo(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoResponseRobotDevInfo.devInfo)
  return devinfo_.Get(index);
}
inline ::aubo::robot::communication::OurRobotDevInfo* ProtoResponseRobotDevInfo::mutable_devinfo(int index) {
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ProtoResponseRobotDevInfo.devInfo)
  return devinfo_.Mutable(index);
}
inline ::aubo::robot::communication::OurRobotDevInfo* ProtoResponseRobotDevInfo::add_devinfo() {
  // @@protoc_insertion_point(field_add:aubo.robot.common.ProtoResponseRobotDevInfo.devInfo)
  return devinfo_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::OurRobotDevInfo >&
ProtoResponseRobotDevInfo::devinfo() const {
  // @@protoc_insertion_point(field_list:aubo.robot.common.ProtoResponseRobotDevInfo.devInfo)
  return devinfo_;
}
inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::OurRobotDevInfo >*
ProtoResponseRobotDevInfo::mutable_devinfo() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.common.ProtoResponseRobotDevInfo.devInfo)
  return &devinfo_;
}

// required .aubo.robot.common.RobotCommonResponse errorInfo = 2;
inline bool ProtoResponseRobotDevInfo::has_errorinfo() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoResponseRobotDevInfo::set_has_errorinfo() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoResponseRobotDevInfo::clear_has_errorinfo() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoResponseRobotDevInfo::clear_errorinfo() {
  if (errorinfo_ != NULL) errorinfo_->::aubo::robot::common::RobotCommonResponse::Clear();
  clear_has_errorinfo();
}
inline const ::aubo::robot::common::RobotCommonResponse& ProtoResponseRobotDevInfo::errorinfo() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoResponseRobotDevInfo.errorInfo)
  return errorinfo_ != NULL ? *errorinfo_ : *default_instance_->errorinfo_;
}
inline ::aubo::robot::common::RobotCommonResponse* ProtoResponseRobotDevInfo::mutable_errorinfo() {
  set_has_errorinfo();
  if (errorinfo_ == NULL) errorinfo_ = new ::aubo::robot::common::RobotCommonResponse;
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ProtoResponseRobotDevInfo.errorInfo)
  return errorinfo_;
}
inline ::aubo::robot::common::RobotCommonResponse* ProtoResponseRobotDevInfo::release_errorinfo() {
  clear_has_errorinfo();
  ::aubo::robot::common::RobotCommonResponse* temp = errorinfo_;
  errorinfo_ = NULL;
  return temp;
}
inline void ProtoResponseRobotDevInfo::set_allocated_errorinfo(::aubo::robot::common::RobotCommonResponse* errorinfo) {
  delete errorinfo_;
  errorinfo_ = errorinfo;
  if (errorinfo) {
    set_has_errorinfo();
  } else {
    clear_has_errorinfo();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.ProtoResponseRobotDevInfo.errorInfo)
}

// -------------------------------------------------------------------

// ToolInertia

// required double xx = 1;
inline bool ToolInertia::has_xx() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ToolInertia::set_has_xx() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ToolInertia::clear_has_xx() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ToolInertia::clear_xx() {
  xx_ = 0;
  clear_has_xx();
}
inline double ToolInertia::xx() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ToolInertia.xx)
  return xx_;
}
inline void ToolInertia::set_xx(double value) {
  set_has_xx();
  xx_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ToolInertia.xx)
}

// required double xy = 2;
inline bool ToolInertia::has_xy() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ToolInertia::set_has_xy() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ToolInertia::clear_has_xy() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ToolInertia::clear_xy() {
  xy_ = 0;
  clear_has_xy();
}
inline double ToolInertia::xy() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ToolInertia.xy)
  return xy_;
}
inline void ToolInertia::set_xy(double value) {
  set_has_xy();
  xy_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ToolInertia.xy)
}

// required double xz = 3;
inline bool ToolInertia::has_xz() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void ToolInertia::set_has_xz() {
  _has_bits_[0] |= 0x00000004u;
}
inline void ToolInertia::clear_has_xz() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void ToolInertia::clear_xz() {
  xz_ = 0;
  clear_has_xz();
}
inline double ToolInertia::xz() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ToolInertia.xz)
  return xz_;
}
inline void ToolInertia::set_xz(double value) {
  set_has_xz();
  xz_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ToolInertia.xz)
}

// required double yy = 4;
inline bool ToolInertia::has_yy() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void ToolInertia::set_has_yy() {
  _has_bits_[0] |= 0x00000008u;
}
inline void ToolInertia::clear_has_yy() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void ToolInertia::clear_yy() {
  yy_ = 0;
  clear_has_yy();
}
inline double ToolInertia::yy() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ToolInertia.yy)
  return yy_;
}
inline void ToolInertia::set_yy(double value) {
  set_has_yy();
  yy_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ToolInertia.yy)
}

// required double yz = 5;
inline bool ToolInertia::has_yz() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void ToolInertia::set_has_yz() {
  _has_bits_[0] |= 0x00000010u;
}
inline void ToolInertia::clear_has_yz() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void ToolInertia::clear_yz() {
  yz_ = 0;
  clear_has_yz();
}
inline double ToolInertia::yz() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ToolInertia.yz)
  return yz_;
}
inline void ToolInertia::set_yz(double value) {
  set_has_yz();
  yz_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ToolInertia.yz)
}

// required double zz = 6;
inline bool ToolInertia::has_zz() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void ToolInertia::set_has_zz() {
  _has_bits_[0] |= 0x00000020u;
}
inline void ToolInertia::clear_has_zz() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void ToolInertia::clear_zz() {
  zz_ = 0;
  clear_has_zz();
}
inline double ToolInertia::zz() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ToolInertia.zz)
  return zz_;
}
inline void ToolInertia::set_zz(double value) {
  set_has_zz();
  zz_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ToolInertia.zz)
}

// -------------------------------------------------------------------

// ToolDynamicsParam

// required double positionX = 1;
inline bool ToolDynamicsParam::has_positionx() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ToolDynamicsParam::set_has_positionx() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ToolDynamicsParam::clear_has_positionx() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ToolDynamicsParam::clear_positionx() {
  positionx_ = 0;
  clear_has_positionx();
}
inline double ToolDynamicsParam::positionx() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ToolDynamicsParam.positionX)
  return positionx_;
}
inline void ToolDynamicsParam::set_positionx(double value) {
  set_has_positionx();
  positionx_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ToolDynamicsParam.positionX)
}

// required double positionY = 2;
inline bool ToolDynamicsParam::has_positiony() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ToolDynamicsParam::set_has_positiony() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ToolDynamicsParam::clear_has_positiony() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ToolDynamicsParam::clear_positiony() {
  positiony_ = 0;
  clear_has_positiony();
}
inline double ToolDynamicsParam::positiony() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ToolDynamicsParam.positionY)
  return positiony_;
}
inline void ToolDynamicsParam::set_positiony(double value) {
  set_has_positiony();
  positiony_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ToolDynamicsParam.positionY)
}

// required double positionZ = 3;
inline bool ToolDynamicsParam::has_positionz() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void ToolDynamicsParam::set_has_positionz() {
  _has_bits_[0] |= 0x00000004u;
}
inline void ToolDynamicsParam::clear_has_positionz() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void ToolDynamicsParam::clear_positionz() {
  positionz_ = 0;
  clear_has_positionz();
}
inline double ToolDynamicsParam::positionz() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ToolDynamicsParam.positionZ)
  return positionz_;
}
inline void ToolDynamicsParam::set_positionz(double value) {
  set_has_positionz();
  positionz_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ToolDynamicsParam.positionZ)
}

// required double payload = 4;
inline bool ToolDynamicsParam::has_payload() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void ToolDynamicsParam::set_has_payload() {
  _has_bits_[0] |= 0x00000008u;
}
inline void ToolDynamicsParam::clear_has_payload() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void ToolDynamicsParam::clear_payload() {
  payload_ = 0;
  clear_has_payload();
}
inline double ToolDynamicsParam::payload() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ToolDynamicsParam.payload)
  return payload_;
}
inline void ToolDynamicsParam::set_payload(double value) {
  set_has_payload();
  payload_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ToolDynamicsParam.payload)
}

// required .aubo.robot.common.ToolInertia toolInertia = 5;
inline bool ToolDynamicsParam::has_toolinertia() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void ToolDynamicsParam::set_has_toolinertia() {
  _has_bits_[0] |= 0x00000010u;
}
inline void ToolDynamicsParam::clear_has_toolinertia() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void ToolDynamicsParam::clear_toolinertia() {
  if (toolinertia_ != NULL) toolinertia_->::aubo::robot::common::ToolInertia::Clear();
  clear_has_toolinertia();
}
inline const ::aubo::robot::common::ToolInertia& ToolDynamicsParam::toolinertia() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ToolDynamicsParam.toolInertia)
  return toolinertia_ != NULL ? *toolinertia_ : *default_instance_->toolinertia_;
}
inline ::aubo::robot::common::ToolInertia* ToolDynamicsParam::mutable_toolinertia() {
  set_has_toolinertia();
  if (toolinertia_ == NULL) toolinertia_ = new ::aubo::robot::common::ToolInertia;
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ToolDynamicsParam.toolInertia)
  return toolinertia_;
}
inline ::aubo::robot::common::ToolInertia* ToolDynamicsParam::release_toolinertia() {
  clear_has_toolinertia();
  ::aubo::robot::common::ToolInertia* temp = toolinertia_;
  toolinertia_ = NULL;
  return temp;
}
inline void ToolDynamicsParam::set_allocated_toolinertia(::aubo::robot::common::ToolInertia* toolinertia) {
  delete toolinertia_;
  toolinertia_ = toolinertia;
  if (toolinertia) {
    set_has_toolinertia();
  } else {
    clear_has_toolinertia();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.ToolDynamicsParam.toolInertia)
}

// -------------------------------------------------------------------

// ToolKinematicsParam

// required .aubo.robot.common.Pos toolPosition = 1;
inline bool ToolKinematicsParam::has_toolposition() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ToolKinematicsParam::set_has_toolposition() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ToolKinematicsParam::clear_has_toolposition() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ToolKinematicsParam::clear_toolposition() {
  if (toolposition_ != NULL) toolposition_->::aubo::robot::common::Pos::Clear();
  clear_has_toolposition();
}
inline const ::aubo::robot::common::Pos& ToolKinematicsParam::toolposition() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ToolKinematicsParam.toolPosition)
  return toolposition_ != NULL ? *toolposition_ : *default_instance_->toolposition_;
}
inline ::aubo::robot::common::Pos* ToolKinematicsParam::mutable_toolposition() {
  set_has_toolposition();
  if (toolposition_ == NULL) toolposition_ = new ::aubo::robot::common::Pos;
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ToolKinematicsParam.toolPosition)
  return toolposition_;
}
inline ::aubo::robot::common::Pos* ToolKinematicsParam::release_toolposition() {
  clear_has_toolposition();
  ::aubo::robot::common::Pos* temp = toolposition_;
  toolposition_ = NULL;
  return temp;
}
inline void ToolKinematicsParam::set_allocated_toolposition(::aubo::robot::common::Pos* toolposition) {
  delete toolposition_;
  toolposition_ = toolposition;
  if (toolposition) {
    set_has_toolposition();
  } else {
    clear_has_toolposition();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.ToolKinematicsParam.toolPosition)
}

// required .aubo.robot.common.Ori toolOri = 2;
inline bool ToolKinematicsParam::has_toolori() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ToolKinematicsParam::set_has_toolori() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ToolKinematicsParam::clear_has_toolori() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ToolKinematicsParam::clear_toolori() {
  if (toolori_ != NULL) toolori_->::aubo::robot::common::Ori::Clear();
  clear_has_toolori();
}
inline const ::aubo::robot::common::Ori& ToolKinematicsParam::toolori() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ToolKinematicsParam.toolOri)
  return toolori_ != NULL ? *toolori_ : *default_instance_->toolori_;
}
inline ::aubo::robot::common::Ori* ToolKinematicsParam::mutable_toolori() {
  set_has_toolori();
  if (toolori_ == NULL) toolori_ = new ::aubo::robot::common::Ori;
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ToolKinematicsParam.toolOri)
  return toolori_;
}
inline ::aubo::robot::common::Ori* ToolKinematicsParam::release_toolori() {
  clear_has_toolori();
  ::aubo::robot::common::Ori* temp = toolori_;
  toolori_ = NULL;
  return temp;
}
inline void ToolKinematicsParam::set_allocated_toolori(::aubo::robot::common::Ori* toolori) {
  delete toolori_;
  toolori_ = toolori;
  if (toolori) {
    set_has_toolori();
  } else {
    clear_has_toolori();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.ToolKinematicsParam.toolOri)
}

// -------------------------------------------------------------------

// ToolParam

// repeated .aubo.robot.common.ToolDynamicsParam toolDynamicsParam = 1;
inline int ToolParam::tooldynamicsparam_size() const {
  return tooldynamicsparam_.size();
}
inline void ToolParam::clear_tooldynamicsparam() {
  tooldynamicsparam_.Clear();
}
inline const ::aubo::robot::common::ToolDynamicsParam& ToolParam::tooldynamicsparam(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ToolParam.toolDynamicsParam)
  return tooldynamicsparam_.Get(index);
}
inline ::aubo::robot::common::ToolDynamicsParam* ToolParam::mutable_tooldynamicsparam(int index) {
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ToolParam.toolDynamicsParam)
  return tooldynamicsparam_.Mutable(index);
}
inline ::aubo::robot::common::ToolDynamicsParam* ToolParam::add_tooldynamicsparam() {
  // @@protoc_insertion_point(field_add:aubo.robot.common.ToolParam.toolDynamicsParam)
  return tooldynamicsparam_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ToolDynamicsParam >&
ToolParam::tooldynamicsparam() const {
  // @@protoc_insertion_point(field_list:aubo.robot.common.ToolParam.toolDynamicsParam)
  return tooldynamicsparam_;
}
inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ToolDynamicsParam >*
ToolParam::mutable_tooldynamicsparam() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.common.ToolParam.toolDynamicsParam)
  return &tooldynamicsparam_;
}

// repeated .aubo.robot.common.ToolKinematicsParam toolKinematicsParam = 2;
inline int ToolParam::toolkinematicsparam_size() const {
  return toolkinematicsparam_.size();
}
inline void ToolParam::clear_toolkinematicsparam() {
  toolkinematicsparam_.Clear();
}
inline const ::aubo::robot::common::ToolKinematicsParam& ToolParam::toolkinematicsparam(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ToolParam.toolKinematicsParam)
  return toolkinematicsparam_.Get(index);
}
inline ::aubo::robot::common::ToolKinematicsParam* ToolParam::mutable_toolkinematicsparam(int index) {
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ToolParam.toolKinematicsParam)
  return toolkinematicsparam_.Mutable(index);
}
inline ::aubo::robot::common::ToolKinematicsParam* ToolParam::add_toolkinematicsparam() {
  // @@protoc_insertion_point(field_add:aubo.robot.common.ToolParam.toolKinematicsParam)
  return toolkinematicsparam_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ToolKinematicsParam >&
ToolParam::toolkinematicsparam() const {
  // @@protoc_insertion_point(field_list:aubo.robot.common.ToolParam.toolKinematicsParam)
  return toolkinematicsparam_;
}
inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ToolKinematicsParam >*
ToolParam::mutable_toolkinematicsparam() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.common.ToolParam.toolKinematicsParam)
  return &toolkinematicsparam_;
}

// required .aubo.robot.common.RobotCommonResponse errorInfo = 3;
inline bool ToolParam::has_errorinfo() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void ToolParam::set_has_errorinfo() {
  _has_bits_[0] |= 0x00000004u;
}
inline void ToolParam::clear_has_errorinfo() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void ToolParam::clear_errorinfo() {
  if (errorinfo_ != NULL) errorinfo_->::aubo::robot::common::RobotCommonResponse::Clear();
  clear_has_errorinfo();
}
inline const ::aubo::robot::common::RobotCommonResponse& ToolParam::errorinfo() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ToolParam.errorInfo)
  return errorinfo_ != NULL ? *errorinfo_ : *default_instance_->errorinfo_;
}
inline ::aubo::robot::common::RobotCommonResponse* ToolParam::mutable_errorinfo() {
  set_has_errorinfo();
  if (errorinfo_ == NULL) errorinfo_ = new ::aubo::robot::common::RobotCommonResponse;
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ToolParam.errorInfo)
  return errorinfo_;
}
inline ::aubo::robot::common::RobotCommonResponse* ToolParam::release_errorinfo() {
  clear_has_errorinfo();
  ::aubo::robot::common::RobotCommonResponse* temp = errorinfo_;
  errorinfo_ = NULL;
  return temp;
}
inline void ToolParam::set_allocated_errorinfo(::aubo::robot::common::RobotCommonResponse* errorinfo) {
  delete errorinfo_;
  errorinfo_ = errorinfo;
  if (errorinfo) {
    set_has_errorinfo();
  } else {
    clear_has_errorinfo();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.ToolParam.errorInfo)
}

// -------------------------------------------------------------------

// ProtoConveyorTrackValuePoint

// required .aubo.robot.common.Pos position = 1;
inline bool ProtoConveyorTrackValuePoint::has_position() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoConveyorTrackValuePoint::set_has_position() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoConveyorTrackValuePoint::clear_has_position() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoConveyorTrackValuePoint::clear_position() {
  if (position_ != NULL) position_->::aubo::robot::common::Pos::Clear();
  clear_has_position();
}
inline const ::aubo::robot::common::Pos& ProtoConveyorTrackValuePoint::position() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoConveyorTrackValuePoint.position)
  return position_ != NULL ? *position_ : *default_instance_->position_;
}
inline ::aubo::robot::common::Pos* ProtoConveyorTrackValuePoint::mutable_position() {
  set_has_position();
  if (position_ == NULL) position_ = new ::aubo::robot::common::Pos;
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ProtoConveyorTrackValuePoint.position)
  return position_;
}
inline ::aubo::robot::common::Pos* ProtoConveyorTrackValuePoint::release_position() {
  clear_has_position();
  ::aubo::robot::common::Pos* temp = position_;
  position_ = NULL;
  return temp;
}
inline void ProtoConveyorTrackValuePoint::set_allocated_position(::aubo::robot::common::Pos* position) {
  delete position_;
  position_ = position;
  if (position) {
    set_has_position();
  } else {
    clear_has_position();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.ProtoConveyorTrackValuePoint.position)
}

// required .aubo.robot.common.Ori ori = 2;
inline bool ProtoConveyorTrackValuePoint::has_ori() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoConveyorTrackValuePoint::set_has_ori() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoConveyorTrackValuePoint::clear_has_ori() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoConveyorTrackValuePoint::clear_ori() {
  if (ori_ != NULL) ori_->::aubo::robot::common::Ori::Clear();
  clear_has_ori();
}
inline const ::aubo::robot::common::Ori& ProtoConveyorTrackValuePoint::ori() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoConveyorTrackValuePoint.ori)
  return ori_ != NULL ? *ori_ : *default_instance_->ori_;
}
inline ::aubo::robot::common::Ori* ProtoConveyorTrackValuePoint::mutable_ori() {
  set_has_ori();
  if (ori_ == NULL) ori_ = new ::aubo::robot::common::Ori;
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ProtoConveyorTrackValuePoint.ori)
  return ori_;
}
inline ::aubo::robot::common::Ori* ProtoConveyorTrackValuePoint::release_ori() {
  clear_has_ori();
  ::aubo::robot::common::Ori* temp = ori_;
  ori_ = NULL;
  return temp;
}
inline void ProtoConveyorTrackValuePoint::set_allocated_ori(::aubo::robot::common::Ori* ori) {
  delete ori_;
  ori_ = ori;
  if (ori) {
    set_has_ori();
  } else {
    clear_has_ori();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.ProtoConveyorTrackValuePoint.ori)
}

// required int32 timestamp = 3;
inline bool ProtoConveyorTrackValuePoint::has_timestamp() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void ProtoConveyorTrackValuePoint::set_has_timestamp() {
  _has_bits_[0] |= 0x00000004u;
}
inline void ProtoConveyorTrackValuePoint::clear_has_timestamp() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void ProtoConveyorTrackValuePoint::clear_timestamp() {
  timestamp_ = 0;
  clear_has_timestamp();
}
inline ::google::protobuf::int32 ProtoConveyorTrackValuePoint::timestamp() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoConveyorTrackValuePoint.timestamp)
  return timestamp_;
}
inline void ProtoConveyorTrackValuePoint::set_timestamp(::google::protobuf::int32 value) {
  set_has_timestamp();
  timestamp_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoConveyorTrackValuePoint.timestamp)
}

// -------------------------------------------------------------------

// ProtoRobotSafetyConfig

// repeated int32 robotReducedConfigJointSpeed = 1;
inline int ProtoRobotSafetyConfig::robotreducedconfigjointspeed_size() const {
  return robotreducedconfigjointspeed_.size();
}
inline void ProtoRobotSafetyConfig::clear_robotreducedconfigjointspeed() {
  robotreducedconfigjointspeed_.Clear();
}
inline ::google::protobuf::int32 ProtoRobotSafetyConfig::robotreducedconfigjointspeed(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoRobotSafetyConfig.robotReducedConfigJointSpeed)
  return robotreducedconfigjointspeed_.Get(index);
}
inline void ProtoRobotSafetyConfig::set_robotreducedconfigjointspeed(int index, ::google::protobuf::int32 value) {
  robotreducedconfigjointspeed_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoRobotSafetyConfig.robotReducedConfigJointSpeed)
}
inline void ProtoRobotSafetyConfig::add_robotreducedconfigjointspeed(::google::protobuf::int32 value) {
  robotreducedconfigjointspeed_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.common.ProtoRobotSafetyConfig.robotReducedConfigJointSpeed)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
ProtoRobotSafetyConfig::robotreducedconfigjointspeed() const {
  // @@protoc_insertion_point(field_list:aubo.robot.common.ProtoRobotSafetyConfig.robotReducedConfigJointSpeed)
  return robotreducedconfigjointspeed_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
ProtoRobotSafetyConfig::mutable_robotreducedconfigjointspeed() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.common.ProtoRobotSafetyConfig.robotReducedConfigJointSpeed)
  return &robotreducedconfigjointspeed_;
}

// required int32 robotReducedConfigTcpSpeed = 2;
inline bool ProtoRobotSafetyConfig::has_robotreducedconfigtcpspeed() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoRobotSafetyConfig::set_has_robotreducedconfigtcpspeed() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoRobotSafetyConfig::clear_has_robotreducedconfigtcpspeed() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoRobotSafetyConfig::clear_robotreducedconfigtcpspeed() {
  robotreducedconfigtcpspeed_ = 0;
  clear_has_robotreducedconfigtcpspeed();
}
inline ::google::protobuf::int32 ProtoRobotSafetyConfig::robotreducedconfigtcpspeed() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoRobotSafetyConfig.robotReducedConfigTcpSpeed)
  return robotreducedconfigtcpspeed_;
}
inline void ProtoRobotSafetyConfig::set_robotreducedconfigtcpspeed(::google::protobuf::int32 value) {
  set_has_robotreducedconfigtcpspeed();
  robotreducedconfigtcpspeed_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoRobotSafetyConfig.robotReducedConfigTcpSpeed)
}

// required int32 robotReducedConfigTcpForce = 3;
inline bool ProtoRobotSafetyConfig::has_robotreducedconfigtcpforce() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void ProtoRobotSafetyConfig::set_has_robotreducedconfigtcpforce() {
  _has_bits_[0] |= 0x00000004u;
}
inline void ProtoRobotSafetyConfig::clear_has_robotreducedconfigtcpforce() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void ProtoRobotSafetyConfig::clear_robotreducedconfigtcpforce() {
  robotreducedconfigtcpforce_ = 0;
  clear_has_robotreducedconfigtcpforce();
}
inline ::google::protobuf::int32 ProtoRobotSafetyConfig::robotreducedconfigtcpforce() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoRobotSafetyConfig.robotReducedConfigTcpForce)
  return robotreducedconfigtcpforce_;
}
inline void ProtoRobotSafetyConfig::set_robotreducedconfigtcpforce(::google::protobuf::int32 value) {
  set_has_robotreducedconfigtcpforce();
  robotreducedconfigtcpforce_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoRobotSafetyConfig.robotReducedConfigTcpForce)
}

// required int32 robotReducedConfigMomentum = 4;
inline bool ProtoRobotSafetyConfig::has_robotreducedconfigmomentum() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void ProtoRobotSafetyConfig::set_has_robotreducedconfigmomentum() {
  _has_bits_[0] |= 0x00000008u;
}
inline void ProtoRobotSafetyConfig::clear_has_robotreducedconfigmomentum() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void ProtoRobotSafetyConfig::clear_robotreducedconfigmomentum() {
  robotreducedconfigmomentum_ = 0;
  clear_has_robotreducedconfigmomentum();
}
inline ::google::protobuf::int32 ProtoRobotSafetyConfig::robotreducedconfigmomentum() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoRobotSafetyConfig.robotReducedConfigMomentum)
  return robotreducedconfigmomentum_;
}
inline void ProtoRobotSafetyConfig::set_robotreducedconfigmomentum(::google::protobuf::int32 value) {
  set_has_robotreducedconfigmomentum();
  robotreducedconfigmomentum_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoRobotSafetyConfig.robotReducedConfigMomentum)
}

// required int32 robotReducedConfigPower = 5;
inline bool ProtoRobotSafetyConfig::has_robotreducedconfigpower() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void ProtoRobotSafetyConfig::set_has_robotreducedconfigpower() {
  _has_bits_[0] |= 0x00000010u;
}
inline void ProtoRobotSafetyConfig::clear_has_robotreducedconfigpower() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void ProtoRobotSafetyConfig::clear_robotreducedconfigpower() {
  robotreducedconfigpower_ = 0;
  clear_has_robotreducedconfigpower();
}
inline ::google::protobuf::int32 ProtoRobotSafetyConfig::robotreducedconfigpower() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoRobotSafetyConfig.robotReducedConfigPower)
  return robotreducedconfigpower_;
}
inline void ProtoRobotSafetyConfig::set_robotreducedconfigpower(::google::protobuf::int32 value) {
  set_has_robotreducedconfigpower();
  robotreducedconfigpower_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoRobotSafetyConfig.robotReducedConfigPower)
}

// required int32 robotSafeguradResetConfig = 6;
inline bool ProtoRobotSafetyConfig::has_robotsafeguradresetconfig() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void ProtoRobotSafetyConfig::set_has_robotsafeguradresetconfig() {
  _has_bits_[0] |= 0x00000020u;
}
inline void ProtoRobotSafetyConfig::clear_has_robotsafeguradresetconfig() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void ProtoRobotSafetyConfig::clear_robotsafeguradresetconfig() {
  robotsafeguradresetconfig_ = 0;
  clear_has_robotsafeguradresetconfig();
}
inline ::google::protobuf::int32 ProtoRobotSafetyConfig::robotsafeguradresetconfig() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoRobotSafetyConfig.robotSafeguradResetConfig)
  return robotsafeguradresetconfig_;
}
inline void ProtoRobotSafetyConfig::set_robotsafeguradresetconfig(::google::protobuf::int32 value) {
  set_has_robotsafeguradresetconfig();
  robotsafeguradresetconfig_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoRobotSafetyConfig.robotSafeguradResetConfig)
}

// required int32 robotOperationalModeConfig = 7;
inline bool ProtoRobotSafetyConfig::has_robotoperationalmodeconfig() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void ProtoRobotSafetyConfig::set_has_robotoperationalmodeconfig() {
  _has_bits_[0] |= 0x00000040u;
}
inline void ProtoRobotSafetyConfig::clear_has_robotoperationalmodeconfig() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void ProtoRobotSafetyConfig::clear_robotoperationalmodeconfig() {
  robotoperationalmodeconfig_ = 0;
  clear_has_robotoperationalmodeconfig();
}
inline ::google::protobuf::int32 ProtoRobotSafetyConfig::robotoperationalmodeconfig() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoRobotSafetyConfig.robotOperationalModeConfig)
  return robotoperationalmodeconfig_;
}
inline void ProtoRobotSafetyConfig::set_robotoperationalmodeconfig(::google::protobuf::int32 value) {
  set_has_robotoperationalmodeconfig();
  robotoperationalmodeconfig_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoRobotSafetyConfig.robotOperationalModeConfig)
}

// -------------------------------------------------------------------

// ProtoResponseRobotSafetyConfig

// repeated .aubo.robot.common.ProtoRobotSafetyConfig safetyConfig = 1;
inline int ProtoResponseRobotSafetyConfig::safetyconfig_size() const {
  return safetyconfig_.size();
}
inline void ProtoResponseRobotSafetyConfig::clear_safetyconfig() {
  safetyconfig_.Clear();
}
inline const ::aubo::robot::common::ProtoRobotSafetyConfig& ProtoResponseRobotSafetyConfig::safetyconfig(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoResponseRobotSafetyConfig.safetyConfig)
  return safetyconfig_.Get(index);
}
inline ::aubo::robot::common::ProtoRobotSafetyConfig* ProtoResponseRobotSafetyConfig::mutable_safetyconfig(int index) {
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ProtoResponseRobotSafetyConfig.safetyConfig)
  return safetyconfig_.Mutable(index);
}
inline ::aubo::robot::common::ProtoRobotSafetyConfig* ProtoResponseRobotSafetyConfig::add_safetyconfig() {
  // @@protoc_insertion_point(field_add:aubo.robot.common.ProtoResponseRobotSafetyConfig.safetyConfig)
  return safetyconfig_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ProtoRobotSafetyConfig >&
ProtoResponseRobotSafetyConfig::safetyconfig() const {
  // @@protoc_insertion_point(field_list:aubo.robot.common.ProtoResponseRobotSafetyConfig.safetyConfig)
  return safetyconfig_;
}
inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ProtoRobotSafetyConfig >*
ProtoResponseRobotSafetyConfig::mutable_safetyconfig() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.common.ProtoResponseRobotSafetyConfig.safetyConfig)
  return &safetyconfig_;
}

// required .aubo.robot.common.RobotCommonResponse errorInfo = 2;
inline bool ProtoResponseRobotSafetyConfig::has_errorinfo() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoResponseRobotSafetyConfig::set_has_errorinfo() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoResponseRobotSafetyConfig::clear_has_errorinfo() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoResponseRobotSafetyConfig::clear_errorinfo() {
  if (errorinfo_ != NULL) errorinfo_->::aubo::robot::common::RobotCommonResponse::Clear();
  clear_has_errorinfo();
}
inline const ::aubo::robot::common::RobotCommonResponse& ProtoResponseRobotSafetyConfig::errorinfo() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoResponseRobotSafetyConfig.errorInfo)
  return errorinfo_ != NULL ? *errorinfo_ : *default_instance_->errorinfo_;
}
inline ::aubo::robot::common::RobotCommonResponse* ProtoResponseRobotSafetyConfig::mutable_errorinfo() {
  set_has_errorinfo();
  if (errorinfo_ == NULL) errorinfo_ = new ::aubo::robot::common::RobotCommonResponse;
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ProtoResponseRobotSafetyConfig.errorInfo)
  return errorinfo_;
}
inline ::aubo::robot::common::RobotCommonResponse* ProtoResponseRobotSafetyConfig::release_errorinfo() {
  clear_has_errorinfo();
  ::aubo::robot::common::RobotCommonResponse* temp = errorinfo_;
  errorinfo_ = NULL;
  return temp;
}
inline void ProtoResponseRobotSafetyConfig::set_allocated_errorinfo(::aubo::robot::common::RobotCommonResponse* errorinfo) {
  delete errorinfo_;
  errorinfo_ = errorinfo;
  if (errorinfo) {
    set_has_errorinfo();
  } else {
    clear_has_errorinfo();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.ProtoResponseRobotSafetyConfig.errorInfo)
}

// -------------------------------------------------------------------

// ProtoOrpeSafetyStatus

// required int32 orpePause = 1;
inline bool ProtoOrpeSafetyStatus::has_orpepause() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoOrpeSafetyStatus::set_has_orpepause() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoOrpeSafetyStatus::clear_has_orpepause() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoOrpeSafetyStatus::clear_orpepause() {
  orpepause_ = 0;
  clear_has_orpepause();
}
inline ::google::protobuf::int32 ProtoOrpeSafetyStatus::orpepause() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoOrpeSafetyStatus.orpePause)
  return orpepause_;
}
inline void ProtoOrpeSafetyStatus::set_orpepause(::google::protobuf::int32 value) {
  set_has_orpepause();
  orpepause_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoOrpeSafetyStatus.orpePause)
}

// required int32 orpeStop = 2;
inline bool ProtoOrpeSafetyStatus::has_orpestop() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoOrpeSafetyStatus::set_has_orpestop() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoOrpeSafetyStatus::clear_has_orpestop() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoOrpeSafetyStatus::clear_orpestop() {
  orpestop_ = 0;
  clear_has_orpestop();
}
inline ::google::protobuf::int32 ProtoOrpeSafetyStatus::orpestop() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoOrpeSafetyStatus.orpeStop)
  return orpestop_;
}
inline void ProtoOrpeSafetyStatus::set_orpestop(::google::protobuf::int32 value) {
  set_has_orpestop();
  orpestop_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoOrpeSafetyStatus.orpeStop)
}

// repeated uint32 orpeError = 3;
inline int ProtoOrpeSafetyStatus::orpeerror_size() const {
  return orpeerror_.size();
}
inline void ProtoOrpeSafetyStatus::clear_orpeerror() {
  orpeerror_.Clear();
}
inline ::google::protobuf::uint32 ProtoOrpeSafetyStatus::orpeerror(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoOrpeSafetyStatus.orpeError)
  return orpeerror_.Get(index);
}
inline void ProtoOrpeSafetyStatus::set_orpeerror(int index, ::google::protobuf::uint32 value) {
  orpeerror_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoOrpeSafetyStatus.orpeError)
}
inline void ProtoOrpeSafetyStatus::add_orpeerror(::google::protobuf::uint32 value) {
  orpeerror_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.common.ProtoOrpeSafetyStatus.orpeError)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
ProtoOrpeSafetyStatus::orpeerror() const {
  // @@protoc_insertion_point(field_list:aubo.robot.common.ProtoOrpeSafetyStatus.orpeError)
  return orpeerror_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
ProtoOrpeSafetyStatus::mutable_orpeerror() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.common.ProtoOrpeSafetyStatus.orpeError)
  return &orpeerror_;
}

// required int32 systemEmergencyStop = 4;
inline bool ProtoOrpeSafetyStatus::has_systememergencystop() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void ProtoOrpeSafetyStatus::set_has_systememergencystop() {
  _has_bits_[0] |= 0x00000008u;
}
inline void ProtoOrpeSafetyStatus::clear_has_systememergencystop() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void ProtoOrpeSafetyStatus::clear_systememergencystop() {
  systememergencystop_ = 0;
  clear_has_systememergencystop();
}
inline ::google::protobuf::int32 ProtoOrpeSafetyStatus::systememergencystop() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoOrpeSafetyStatus.systemEmergencyStop)
  return systememergencystop_;
}
inline void ProtoOrpeSafetyStatus::set_systememergencystop(::google::protobuf::int32 value) {
  set_has_systememergencystop();
  systememergencystop_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoOrpeSafetyStatus.systemEmergencyStop)
}

// required int32 reducedModeError = 5;
inline bool ProtoOrpeSafetyStatus::has_reducedmodeerror() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void ProtoOrpeSafetyStatus::set_has_reducedmodeerror() {
  _has_bits_[0] |= 0x00000010u;
}
inline void ProtoOrpeSafetyStatus::clear_has_reducedmodeerror() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void ProtoOrpeSafetyStatus::clear_reducedmodeerror() {
  reducedmodeerror_ = 0;
  clear_has_reducedmodeerror();
}
inline ::google::protobuf::int32 ProtoOrpeSafetyStatus::reducedmodeerror() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoOrpeSafetyStatus.reducedModeError)
  return reducedmodeerror_;
}
inline void ProtoOrpeSafetyStatus::set_reducedmodeerror(::google::protobuf::int32 value) {
  set_has_reducedmodeerror();
  reducedmodeerror_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoOrpeSafetyStatus.reducedModeError)
}

// required int32 safetyguardResetSucc = 6;
inline bool ProtoOrpeSafetyStatus::has_safetyguardresetsucc() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void ProtoOrpeSafetyStatus::set_has_safetyguardresetsucc() {
  _has_bits_[0] |= 0x00000020u;
}
inline void ProtoOrpeSafetyStatus::clear_has_safetyguardresetsucc() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void ProtoOrpeSafetyStatus::clear_safetyguardresetsucc() {
  safetyguardresetsucc_ = 0;
  clear_has_safetyguardresetsucc();
}
inline ::google::protobuf::int32 ProtoOrpeSafetyStatus::safetyguardresetsucc() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoOrpeSafetyStatus.safetyguardResetSucc)
  return safetyguardresetsucc_;
}
inline void ProtoOrpeSafetyStatus::set_safetyguardresetsucc(::google::protobuf::int32 value) {
  set_has_safetyguardresetsucc();
  safetyguardresetsucc_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoOrpeSafetyStatus.safetyguardResetSucc)
}

// -------------------------------------------------------------------

// ProtoResponseOrpeSafetyStatus

// repeated .aubo.robot.common.ProtoOrpeSafetyStatus safetyStatus = 1;
inline int ProtoResponseOrpeSafetyStatus::safetystatus_size() const {
  return safetystatus_.size();
}
inline void ProtoResponseOrpeSafetyStatus::clear_safetystatus() {
  safetystatus_.Clear();
}
inline const ::aubo::robot::common::ProtoOrpeSafetyStatus& ProtoResponseOrpeSafetyStatus::safetystatus(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoResponseOrpeSafetyStatus.safetyStatus)
  return safetystatus_.Get(index);
}
inline ::aubo::robot::common::ProtoOrpeSafetyStatus* ProtoResponseOrpeSafetyStatus::mutable_safetystatus(int index) {
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ProtoResponseOrpeSafetyStatus.safetyStatus)
  return safetystatus_.Mutable(index);
}
inline ::aubo::robot::common::ProtoOrpeSafetyStatus* ProtoResponseOrpeSafetyStatus::add_safetystatus() {
  // @@protoc_insertion_point(field_add:aubo.robot.common.ProtoResponseOrpeSafetyStatus.safetyStatus)
  return safetystatus_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ProtoOrpeSafetyStatus >&
ProtoResponseOrpeSafetyStatus::safetystatus() const {
  // @@protoc_insertion_point(field_list:aubo.robot.common.ProtoResponseOrpeSafetyStatus.safetyStatus)
  return safetystatus_;
}
inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::common::ProtoOrpeSafetyStatus >*
ProtoResponseOrpeSafetyStatus::mutable_safetystatus() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.common.ProtoResponseOrpeSafetyStatus.safetyStatus)
  return &safetystatus_;
}

// required .aubo.robot.common.RobotCommonResponse errorInfo = 2;
inline bool ProtoResponseOrpeSafetyStatus::has_errorinfo() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoResponseOrpeSafetyStatus::set_has_errorinfo() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoResponseOrpeSafetyStatus::clear_has_errorinfo() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoResponseOrpeSafetyStatus::clear_errorinfo() {
  if (errorinfo_ != NULL) errorinfo_->::aubo::robot::common::RobotCommonResponse::Clear();
  clear_has_errorinfo();
}
inline const ::aubo::robot::common::RobotCommonResponse& ProtoResponseOrpeSafetyStatus::errorinfo() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoResponseOrpeSafetyStatus.errorInfo)
  return errorinfo_ != NULL ? *errorinfo_ : *default_instance_->errorinfo_;
}
inline ::aubo::robot::common::RobotCommonResponse* ProtoResponseOrpeSafetyStatus::mutable_errorinfo() {
  set_has_errorinfo();
  if (errorinfo_ == NULL) errorinfo_ = new ::aubo::robot::common::RobotCommonResponse;
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ProtoResponseOrpeSafetyStatus.errorInfo)
  return errorinfo_;
}
inline ::aubo::robot::common::RobotCommonResponse* ProtoResponseOrpeSafetyStatus::release_errorinfo() {
  clear_has_errorinfo();
  ::aubo::robot::common::RobotCommonResponse* temp = errorinfo_;
  errorinfo_ = NULL;
  return temp;
}
inline void ProtoResponseOrpeSafetyStatus::set_allocated_errorinfo(::aubo::robot::common::RobotCommonResponse* errorinfo) {
  delete errorinfo_;
  errorinfo_ = errorinfo;
  if (errorinfo) {
    set_has_errorinfo();
  } else {
    clear_has_errorinfo();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.ProtoResponseOrpeSafetyStatus.errorInfo)
}

// -------------------------------------------------------------------

// DhParam

// required double A3 = 1;
inline bool DhParam::has_a3() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void DhParam::set_has_a3() {
  _has_bits_[0] |= 0x00000001u;
}
inline void DhParam::clear_has_a3() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void DhParam::clear_a3() {
  a3_ = 0;
  clear_has_a3();
}
inline double DhParam::a3() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.DhParam.A3)
  return a3_;
}
inline void DhParam::set_a3(double value) {
  set_has_a3();
  a3_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.DhParam.A3)
}

// required double A4 = 2;
inline bool DhParam::has_a4() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void DhParam::set_has_a4() {
  _has_bits_[0] |= 0x00000002u;
}
inline void DhParam::clear_has_a4() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void DhParam::clear_a4() {
  a4_ = 0;
  clear_has_a4();
}
inline double DhParam::a4() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.DhParam.A4)
  return a4_;
}
inline void DhParam::set_a4(double value) {
  set_has_a4();
  a4_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.DhParam.A4)
}

// required double D1 = 3;
inline bool DhParam::has_d1() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void DhParam::set_has_d1() {
  _has_bits_[0] |= 0x00000004u;
}
inline void DhParam::clear_has_d1() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void DhParam::clear_d1() {
  d1_ = 0;
  clear_has_d1();
}
inline double DhParam::d1() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.DhParam.D1)
  return d1_;
}
inline void DhParam::set_d1(double value) {
  set_has_d1();
  d1_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.DhParam.D1)
}

// required double D2 = 4;
inline bool DhParam::has_d2() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void DhParam::set_has_d2() {
  _has_bits_[0] |= 0x00000008u;
}
inline void DhParam::clear_has_d2() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void DhParam::clear_d2() {
  d2_ = 0;
  clear_has_d2();
}
inline double DhParam::d2() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.DhParam.D2)
  return d2_;
}
inline void DhParam::set_d2(double value) {
  set_has_d2();
  d2_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.DhParam.D2)
}

// required double D5 = 5;
inline bool DhParam::has_d5() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void DhParam::set_has_d5() {
  _has_bits_[0] |= 0x00000010u;
}
inline void DhParam::clear_has_d5() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void DhParam::clear_d5() {
  d5_ = 0;
  clear_has_d5();
}
inline double DhParam::d5() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.DhParam.D5)
  return d5_;
}
inline void DhParam::set_d5(double value) {
  set_has_d5();
  d5_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.DhParam.D5)
}

// required double D6 = 6;
inline bool DhParam::has_d6() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void DhParam::set_has_d6() {
  _has_bits_[0] |= 0x00000020u;
}
inline void DhParam::clear_has_d6() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void DhParam::clear_d6() {
  d6_ = 0;
  clear_has_d6();
}
inline double DhParam::d6() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.DhParam.D6)
  return d6_;
}
inline void DhParam::set_d6(double value) {
  set_has_d6();
  d6_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.DhParam.D6)
}

// repeated double alpha = 7;
inline int DhParam::alpha_size() const {
  return alpha_.size();
}
inline void DhParam::clear_alpha() {
  alpha_.Clear();
}
inline double DhParam::alpha(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.DhParam.alpha)
  return alpha_.Get(index);
}
inline void DhParam::set_alpha(int index, double value) {
  alpha_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.common.DhParam.alpha)
}
inline void DhParam::add_alpha(double value) {
  alpha_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.common.DhParam.alpha)
}
inline const ::google::protobuf::RepeatedField< double >&
DhParam::alpha() const {
  // @@protoc_insertion_point(field_list:aubo.robot.common.DhParam.alpha)
  return alpha_;
}
inline ::google::protobuf::RepeatedField< double >*
DhParam::mutable_alpha() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.common.DhParam.alpha)
  return &alpha_;
}

// repeated double a = 8;
inline int DhParam::a_size() const {
  return a_.size();
}
inline void DhParam::clear_a() {
  a_.Clear();
}
inline double DhParam::a(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.DhParam.a)
  return a_.Get(index);
}
inline void DhParam::set_a(int index, double value) {
  a_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.common.DhParam.a)
}
inline void DhParam::add_a(double value) {
  a_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.common.DhParam.a)
}
inline const ::google::protobuf::RepeatedField< double >&
DhParam::a() const {
  // @@protoc_insertion_point(field_list:aubo.robot.common.DhParam.a)
  return a_;
}
inline ::google::protobuf::RepeatedField< double >*
DhParam::mutable_a() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.common.DhParam.a)
  return &a_;
}

// repeated double d = 9;
inline int DhParam::d_size() const {
  return d_.size();
}
inline void DhParam::clear_d() {
  d_.Clear();
}
inline double DhParam::d(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.DhParam.d)
  return d_.Get(index);
}
inline void DhParam::set_d(int index, double value) {
  d_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.common.DhParam.d)
}
inline void DhParam::add_d(double value) {
  d_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.common.DhParam.d)
}
inline const ::google::protobuf::RepeatedField< double >&
DhParam::d() const {
  // @@protoc_insertion_point(field_list:aubo.robot.common.DhParam.d)
  return d_;
}
inline ::google::protobuf::RepeatedField< double >*
DhParam::mutable_d() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.common.DhParam.d)
  return &d_;
}

// repeated double theta = 10;
inline int DhParam::theta_size() const {
  return theta_.size();
}
inline void DhParam::clear_theta() {
  theta_.Clear();
}
inline double DhParam::theta(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.DhParam.theta)
  return theta_.Get(index);
}
inline void DhParam::set_theta(int index, double value) {
  theta_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.common.DhParam.theta)
}
inline void DhParam::add_theta(double value) {
  theta_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.common.DhParam.theta)
}
inline const ::google::protobuf::RepeatedField< double >&
DhParam::theta() const {
  // @@protoc_insertion_point(field_list:aubo.robot.common.DhParam.theta)
  return theta_;
}
inline ::google::protobuf::RepeatedField< double >*
DhParam::mutable_theta() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.common.DhParam.theta)
  return &theta_;
}

// -------------------------------------------------------------------

// ProtoResponseRobotDhParam

// required int32 robotType = 1;
inline bool ProtoResponseRobotDhParam::has_robottype() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoResponseRobotDhParam::set_has_robottype() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoResponseRobotDhParam::clear_has_robottype() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoResponseRobotDhParam::clear_robottype() {
  robottype_ = 0;
  clear_has_robottype();
}
inline ::google::protobuf::int32 ProtoResponseRobotDhParam::robottype() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoResponseRobotDhParam.robotType)
  return robottype_;
}
inline void ProtoResponseRobotDhParam::set_robottype(::google::protobuf::int32 value) {
  set_has_robottype();
  robottype_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoResponseRobotDhParam.robotType)
}

// required .aubo.robot.common.DhParam dhParam = 2;
inline bool ProtoResponseRobotDhParam::has_dhparam() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoResponseRobotDhParam::set_has_dhparam() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoResponseRobotDhParam::clear_has_dhparam() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoResponseRobotDhParam::clear_dhparam() {
  if (dhparam_ != NULL) dhparam_->::aubo::robot::common::DhParam::Clear();
  clear_has_dhparam();
}
inline const ::aubo::robot::common::DhParam& ProtoResponseRobotDhParam::dhparam() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoResponseRobotDhParam.dhParam)
  return dhparam_ != NULL ? *dhparam_ : *default_instance_->dhparam_;
}
inline ::aubo::robot::common::DhParam* ProtoResponseRobotDhParam::mutable_dhparam() {
  set_has_dhparam();
  if (dhparam_ == NULL) dhparam_ = new ::aubo::robot::common::DhParam;
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ProtoResponseRobotDhParam.dhParam)
  return dhparam_;
}
inline ::aubo::robot::common::DhParam* ProtoResponseRobotDhParam::release_dhparam() {
  clear_has_dhparam();
  ::aubo::robot::common::DhParam* temp = dhparam_;
  dhparam_ = NULL;
  return temp;
}
inline void ProtoResponseRobotDhParam::set_allocated_dhparam(::aubo::robot::common::DhParam* dhparam) {
  delete dhparam_;
  dhparam_ = dhparam;
  if (dhparam) {
    set_has_dhparam();
  } else {
    clear_has_dhparam();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.ProtoResponseRobotDhParam.dhParam)
}

// required .aubo.robot.common.RobotCommonResponse errorInfo = 3;
inline bool ProtoResponseRobotDhParam::has_errorinfo() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void ProtoResponseRobotDhParam::set_has_errorinfo() {
  _has_bits_[0] |= 0x00000004u;
}
inline void ProtoResponseRobotDhParam::clear_has_errorinfo() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void ProtoResponseRobotDhParam::clear_errorinfo() {
  if (errorinfo_ != NULL) errorinfo_->::aubo::robot::common::RobotCommonResponse::Clear();
  clear_has_errorinfo();
}
inline const ::aubo::robot::common::RobotCommonResponse& ProtoResponseRobotDhParam::errorinfo() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoResponseRobotDhParam.errorInfo)
  return errorinfo_ != NULL ? *errorinfo_ : *default_instance_->errorinfo_;
}
inline ::aubo::robot::common::RobotCommonResponse* ProtoResponseRobotDhParam::mutable_errorinfo() {
  set_has_errorinfo();
  if (errorinfo_ == NULL) errorinfo_ = new ::aubo::robot::common::RobotCommonResponse;
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ProtoResponseRobotDhParam.errorInfo)
  return errorinfo_;
}
inline ::aubo::robot::common::RobotCommonResponse* ProtoResponseRobotDhParam::release_errorinfo() {
  clear_has_errorinfo();
  ::aubo::robot::common::RobotCommonResponse* temp = errorinfo_;
  errorinfo_ = NULL;
  return temp;
}
inline void ProtoResponseRobotDhParam::set_allocated_errorinfo(::aubo::robot::common::RobotCommonResponse* errorinfo) {
  delete errorinfo_;
  errorinfo_ = errorinfo;
  if (errorinfo) {
    set_has_errorinfo();
  } else {
    clear_has_errorinfo();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.ProtoResponseRobotDhParam.errorInfo)
}

// -------------------------------------------------------------------

// RobotJointOffset

// required double joint1 = 1;
inline bool RobotJointOffset::has_joint1() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void RobotJointOffset::set_has_joint1() {
  _has_bits_[0] |= 0x00000001u;
}
inline void RobotJointOffset::clear_has_joint1() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void RobotJointOffset::clear_joint1() {
  joint1_ = 0;
  clear_has_joint1();
}
inline double RobotJointOffset::joint1() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.RobotJointOffset.joint1)
  return joint1_;
}
inline void RobotJointOffset::set_joint1(double value) {
  set_has_joint1();
  joint1_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.RobotJointOffset.joint1)
}

// required double joint2 = 2;
inline bool RobotJointOffset::has_joint2() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void RobotJointOffset::set_has_joint2() {
  _has_bits_[0] |= 0x00000002u;
}
inline void RobotJointOffset::clear_has_joint2() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void RobotJointOffset::clear_joint2() {
  joint2_ = 0;
  clear_has_joint2();
}
inline double RobotJointOffset::joint2() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.RobotJointOffset.joint2)
  return joint2_;
}
inline void RobotJointOffset::set_joint2(double value) {
  set_has_joint2();
  joint2_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.RobotJointOffset.joint2)
}

// required double joint3 = 3;
inline bool RobotJointOffset::has_joint3() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void RobotJointOffset::set_has_joint3() {
  _has_bits_[0] |= 0x00000004u;
}
inline void RobotJointOffset::clear_has_joint3() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void RobotJointOffset::clear_joint3() {
  joint3_ = 0;
  clear_has_joint3();
}
inline double RobotJointOffset::joint3() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.RobotJointOffset.joint3)
  return joint3_;
}
inline void RobotJointOffset::set_joint3(double value) {
  set_has_joint3();
  joint3_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.RobotJointOffset.joint3)
}

// required double joint4 = 4;
inline bool RobotJointOffset::has_joint4() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void RobotJointOffset::set_has_joint4() {
  _has_bits_[0] |= 0x00000008u;
}
inline void RobotJointOffset::clear_has_joint4() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void RobotJointOffset::clear_joint4() {
  joint4_ = 0;
  clear_has_joint4();
}
inline double RobotJointOffset::joint4() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.RobotJointOffset.joint4)
  return joint4_;
}
inline void RobotJointOffset::set_joint4(double value) {
  set_has_joint4();
  joint4_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.RobotJointOffset.joint4)
}

// required double joint5 = 5;
inline bool RobotJointOffset::has_joint5() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void RobotJointOffset::set_has_joint5() {
  _has_bits_[0] |= 0x00000010u;
}
inline void RobotJointOffset::clear_has_joint5() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void RobotJointOffset::clear_joint5() {
  joint5_ = 0;
  clear_has_joint5();
}
inline double RobotJointOffset::joint5() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.RobotJointOffset.joint5)
  return joint5_;
}
inline void RobotJointOffset::set_joint5(double value) {
  set_has_joint5();
  joint5_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.RobotJointOffset.joint5)
}

// required double joint6 = 6;
inline bool RobotJointOffset::has_joint6() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void RobotJointOffset::set_has_joint6() {
  _has_bits_[0] |= 0x00000020u;
}
inline void RobotJointOffset::clear_has_joint6() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void RobotJointOffset::clear_joint6() {
  joint6_ = 0;
  clear_has_joint6();
}
inline double RobotJointOffset::joint6() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.RobotJointOffset.joint6)
  return joint6_;
}
inline void RobotJointOffset::set_joint6(double value) {
  set_has_joint6();
  joint6_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.RobotJointOffset.joint6)
}

// -------------------------------------------------------------------

// ProtoRobotMoveFuncResult

// required int32 ret = 1;
inline bool ProtoRobotMoveFuncResult::has_ret() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoRobotMoveFuncResult::set_has_ret() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoRobotMoveFuncResult::clear_has_ret() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoRobotMoveFuncResult::clear_ret() {
  ret_ = 0;
  clear_has_ret();
}
inline ::google::protobuf::int32 ProtoRobotMoveFuncResult::ret() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoRobotMoveFuncResult.ret)
  return ret_;
}
inline void ProtoRobotMoveFuncResult::set_ret(::google::protobuf::int32 value) {
  set_has_ret();
  ret_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoRobotMoveFuncResult.ret)
}

// required .aubo.robot.common.RobotCommonResponse errorInfo = 2;
inline bool ProtoRobotMoveFuncResult::has_errorinfo() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoRobotMoveFuncResult::set_has_errorinfo() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoRobotMoveFuncResult::clear_has_errorinfo() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoRobotMoveFuncResult::clear_errorinfo() {
  if (errorinfo_ != NULL) errorinfo_->::aubo::robot::common::RobotCommonResponse::Clear();
  clear_has_errorinfo();
}
inline const ::aubo::robot::common::RobotCommonResponse& ProtoRobotMoveFuncResult::errorinfo() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoRobotMoveFuncResult.errorInfo)
  return errorinfo_ != NULL ? *errorinfo_ : *default_instance_->errorinfo_;
}
inline ::aubo::robot::common::RobotCommonResponse* ProtoRobotMoveFuncResult::mutable_errorinfo() {
  set_has_errorinfo();
  if (errorinfo_ == NULL) errorinfo_ = new ::aubo::robot::common::RobotCommonResponse;
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ProtoRobotMoveFuncResult.errorInfo)
  return errorinfo_;
}
inline ::aubo::robot::common::RobotCommonResponse* ProtoRobotMoveFuncResult::release_errorinfo() {
  clear_has_errorinfo();
  ::aubo::robot::common::RobotCommonResponse* temp = errorinfo_;
  errorinfo_ = NULL;
  return temp;
}
inline void ProtoRobotMoveFuncResult::set_allocated_errorinfo(::aubo::robot::common::RobotCommonResponse* errorinfo) {
  delete errorinfo_;
  errorinfo_ = errorinfo;
  if (errorinfo) {
    set_has_errorinfo();
  } else {
    clear_has_errorinfo();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.ProtoRobotMoveFuncResult.errorInfo)
}

// -------------------------------------------------------------------

// ProtoSeamTrack_t

// required int32 trackEnable = 1;
inline bool ProtoSeamTrack_t::has_trackenable() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoSeamTrack_t::set_has_trackenable() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoSeamTrack_t::clear_has_trackenable() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoSeamTrack_t::clear_trackenable() {
  trackenable_ = 0;
  clear_has_trackenable();
}
inline ::google::protobuf::int32 ProtoSeamTrack_t::trackenable() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoSeamTrack_t.trackEnable)
  return trackenable_;
}
inline void ProtoSeamTrack_t::set_trackenable(::google::protobuf::int32 value) {
  set_has_trackenable();
  trackenable_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoSeamTrack_t.trackEnable)
}

// required .aubo.robot.common.ProtoRoadPoint currentRoadPoint = 2;
inline bool ProtoSeamTrack_t::has_currentroadpoint() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoSeamTrack_t::set_has_currentroadpoint() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoSeamTrack_t::clear_has_currentroadpoint() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoSeamTrack_t::clear_currentroadpoint() {
  if (currentroadpoint_ != NULL) currentroadpoint_->::aubo::robot::common::ProtoRoadPoint::Clear();
  clear_has_currentroadpoint();
}
inline const ::aubo::robot::common::ProtoRoadPoint& ProtoSeamTrack_t::currentroadpoint() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoSeamTrack_t.currentRoadPoint)
  return currentroadpoint_ != NULL ? *currentroadpoint_ : *default_instance_->currentroadpoint_;
}
inline ::aubo::robot::common::ProtoRoadPoint* ProtoSeamTrack_t::mutable_currentroadpoint() {
  set_has_currentroadpoint();
  if (currentroadpoint_ == NULL) currentroadpoint_ = new ::aubo::robot::common::ProtoRoadPoint;
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ProtoSeamTrack_t.currentRoadPoint)
  return currentroadpoint_;
}
inline ::aubo::robot::common::ProtoRoadPoint* ProtoSeamTrack_t::release_currentroadpoint() {
  clear_has_currentroadpoint();
  ::aubo::robot::common::ProtoRoadPoint* temp = currentroadpoint_;
  currentroadpoint_ = NULL;
  return temp;
}
inline void ProtoSeamTrack_t::set_allocated_currentroadpoint(::aubo::robot::common::ProtoRoadPoint* currentroadpoint) {
  delete currentroadpoint_;
  currentroadpoint_ = currentroadpoint;
  if (currentroadpoint) {
    set_has_currentroadpoint();
  } else {
    clear_has_currentroadpoint();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.ProtoSeamTrack_t.currentRoadPoint)
}

// required .aubo.robot.common.ProtoRoadPoint nextRoadPoint = 3;
inline bool ProtoSeamTrack_t::has_nextroadpoint() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void ProtoSeamTrack_t::set_has_nextroadpoint() {
  _has_bits_[0] |= 0x00000004u;
}
inline void ProtoSeamTrack_t::clear_has_nextroadpoint() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void ProtoSeamTrack_t::clear_nextroadpoint() {
  if (nextroadpoint_ != NULL) nextroadpoint_->::aubo::robot::common::ProtoRoadPoint::Clear();
  clear_has_nextroadpoint();
}
inline const ::aubo::robot::common::ProtoRoadPoint& ProtoSeamTrack_t::nextroadpoint() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoSeamTrack_t.nextRoadPoint)
  return nextroadpoint_ != NULL ? *nextroadpoint_ : *default_instance_->nextroadpoint_;
}
inline ::aubo::robot::common::ProtoRoadPoint* ProtoSeamTrack_t::mutable_nextroadpoint() {
  set_has_nextroadpoint();
  if (nextroadpoint_ == NULL) nextroadpoint_ = new ::aubo::robot::common::ProtoRoadPoint;
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ProtoSeamTrack_t.nextRoadPoint)
  return nextroadpoint_;
}
inline ::aubo::robot::common::ProtoRoadPoint* ProtoSeamTrack_t::release_nextroadpoint() {
  clear_has_nextroadpoint();
  ::aubo::robot::common::ProtoRoadPoint* temp = nextroadpoint_;
  nextroadpoint_ = NULL;
  return temp;
}
inline void ProtoSeamTrack_t::set_allocated_nextroadpoint(::aubo::robot::common::ProtoRoadPoint* nextroadpoint) {
  delete nextroadpoint_;
  nextroadpoint_ = nextroadpoint;
  if (nextroadpoint) {
    set_has_nextroadpoint();
  } else {
    clear_has_nextroadpoint();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.ProtoSeamTrack_t.nextRoadPoint)
}

// required int32 timeInterval = 4;
inline bool ProtoSeamTrack_t::has_timeinterval() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void ProtoSeamTrack_t::set_has_timeinterval() {
  _has_bits_[0] |= 0x00000008u;
}
inline void ProtoSeamTrack_t::clear_has_timeinterval() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void ProtoSeamTrack_t::clear_timeinterval() {
  timeinterval_ = 0;
  clear_has_timeinterval();
}
inline ::google::protobuf::int32 ProtoSeamTrack_t::timeinterval() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoSeamTrack_t.timeInterval)
  return timeinterval_;
}
inline void ProtoSeamTrack_t::set_timeinterval(::google::protobuf::int32 value) {
  set_has_timeinterval();
  timeinterval_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoSeamTrack_t.timeInterval)
}

// repeated double currentPosError = 5;
inline int ProtoSeamTrack_t::currentposerror_size() const {
  return currentposerror_.size();
}
inline void ProtoSeamTrack_t::clear_currentposerror() {
  currentposerror_.Clear();
}
inline double ProtoSeamTrack_t::currentposerror(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoSeamTrack_t.currentPosError)
  return currentposerror_.Get(index);
}
inline void ProtoSeamTrack_t::set_currentposerror(int index, double value) {
  currentposerror_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoSeamTrack_t.currentPosError)
}
inline void ProtoSeamTrack_t::add_currentposerror(double value) {
  currentposerror_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.common.ProtoSeamTrack_t.currentPosError)
}
inline const ::google::protobuf::RepeatedField< double >&
ProtoSeamTrack_t::currentposerror() const {
  // @@protoc_insertion_point(field_list:aubo.robot.common.ProtoSeamTrack_t.currentPosError)
  return currentposerror_;
}
inline ::google::protobuf::RepeatedField< double >*
ProtoSeamTrack_t::mutable_currentposerror() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.common.ProtoSeamTrack_t.currentPosError)
  return &currentposerror_;
}

// required double maxVel = 6;
inline bool ProtoSeamTrack_t::has_maxvel() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void ProtoSeamTrack_t::set_has_maxvel() {
  _has_bits_[0] |= 0x00000020u;
}
inline void ProtoSeamTrack_t::clear_has_maxvel() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void ProtoSeamTrack_t::clear_maxvel() {
  maxvel_ = 0;
  clear_has_maxvel();
}
inline double ProtoSeamTrack_t::maxvel() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoSeamTrack_t.maxVel)
  return maxvel_;
}
inline void ProtoSeamTrack_t::set_maxvel(double value) {
  set_has_maxvel();
  maxvel_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoSeamTrack_t.maxVel)
}

// required double maxAcc = 7;
inline bool ProtoSeamTrack_t::has_maxacc() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void ProtoSeamTrack_t::set_has_maxacc() {
  _has_bits_[0] |= 0x00000040u;
}
inline void ProtoSeamTrack_t::clear_has_maxacc() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void ProtoSeamTrack_t::clear_maxacc() {
  maxacc_ = 0;
  clear_has_maxacc();
}
inline double ProtoSeamTrack_t::maxacc() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoSeamTrack_t.maxAcc)
  return maxacc_;
}
inline void ProtoSeamTrack_t::set_maxacc(double value) {
  set_has_maxacc();
  maxacc_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoSeamTrack_t.maxAcc)
}

// required int32 paraChanged = 8;
inline bool ProtoSeamTrack_t::has_parachanged() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void ProtoSeamTrack_t::set_has_parachanged() {
  _has_bits_[0] |= 0x00000080u;
}
inline void ProtoSeamTrack_t::clear_has_parachanged() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void ProtoSeamTrack_t::clear_parachanged() {
  parachanged_ = 0;
  clear_has_parachanged();
}
inline ::google::protobuf::int32 ProtoSeamTrack_t::parachanged() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoSeamTrack_t.paraChanged)
  return parachanged_;
}
inline void ProtoSeamTrack_t::set_parachanged(::google::protobuf::int32 value) {
  set_has_parachanged();
  parachanged_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.common.ProtoSeamTrack_t.paraChanged)
}

// -------------------------------------------------------------------

// ProtoSeamTrackResponse_t

// required .aubo.robot.common.ProtoSeamTrack_t seamTrack = 1;
inline bool ProtoSeamTrackResponse_t::has_seamtrack() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoSeamTrackResponse_t::set_has_seamtrack() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoSeamTrackResponse_t::clear_has_seamtrack() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoSeamTrackResponse_t::clear_seamtrack() {
  if (seamtrack_ != NULL) seamtrack_->::aubo::robot::common::ProtoSeamTrack_t::Clear();
  clear_has_seamtrack();
}
inline const ::aubo::robot::common::ProtoSeamTrack_t& ProtoSeamTrackResponse_t::seamtrack() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoSeamTrackResponse_t.seamTrack)
  return seamtrack_ != NULL ? *seamtrack_ : *default_instance_->seamtrack_;
}
inline ::aubo::robot::common::ProtoSeamTrack_t* ProtoSeamTrackResponse_t::mutable_seamtrack() {
  set_has_seamtrack();
  if (seamtrack_ == NULL) seamtrack_ = new ::aubo::robot::common::ProtoSeamTrack_t;
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ProtoSeamTrackResponse_t.seamTrack)
  return seamtrack_;
}
inline ::aubo::robot::common::ProtoSeamTrack_t* ProtoSeamTrackResponse_t::release_seamtrack() {
  clear_has_seamtrack();
  ::aubo::robot::common::ProtoSeamTrack_t* temp = seamtrack_;
  seamtrack_ = NULL;
  return temp;
}
inline void ProtoSeamTrackResponse_t::set_allocated_seamtrack(::aubo::robot::common::ProtoSeamTrack_t* seamtrack) {
  delete seamtrack_;
  seamtrack_ = seamtrack;
  if (seamtrack) {
    set_has_seamtrack();
  } else {
    clear_has_seamtrack();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.ProtoSeamTrackResponse_t.seamTrack)
}

// required .aubo.robot.common.RobotCommonResponse errorInfo = 2;
inline bool ProtoSeamTrackResponse_t::has_errorinfo() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoSeamTrackResponse_t::set_has_errorinfo() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoSeamTrackResponse_t::clear_has_errorinfo() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoSeamTrackResponse_t::clear_errorinfo() {
  if (errorinfo_ != NULL) errorinfo_->::aubo::robot::common::RobotCommonResponse::Clear();
  clear_has_errorinfo();
}
inline const ::aubo::robot::common::RobotCommonResponse& ProtoSeamTrackResponse_t::errorinfo() const {
  // @@protoc_insertion_point(field_get:aubo.robot.common.ProtoSeamTrackResponse_t.errorInfo)
  return errorinfo_ != NULL ? *errorinfo_ : *default_instance_->errorinfo_;
}
inline ::aubo::robot::common::RobotCommonResponse* ProtoSeamTrackResponse_t::mutable_errorinfo() {
  set_has_errorinfo();
  if (errorinfo_ == NULL) errorinfo_ = new ::aubo::robot::common::RobotCommonResponse;
  // @@protoc_insertion_point(field_mutable:aubo.robot.common.ProtoSeamTrackResponse_t.errorInfo)
  return errorinfo_;
}
inline ::aubo::robot::common::RobotCommonResponse* ProtoSeamTrackResponse_t::release_errorinfo() {
  clear_has_errorinfo();
  ::aubo::robot::common::RobotCommonResponse* temp = errorinfo_;
  errorinfo_ = NULL;
  return temp;
}
inline void ProtoSeamTrackResponse_t::set_allocated_errorinfo(::aubo::robot::common::RobotCommonResponse* errorinfo) {
  delete errorinfo_;
  errorinfo_ = errorinfo;
  if (errorinfo) {
    set_has_errorinfo();
  } else {
    clear_has_errorinfo();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.common.ProtoSeamTrackResponse_t.errorInfo)
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace common
}  // namespace robot
}  // namespace aubo

#ifndef SWIG
namespace google {
namespace protobuf {


}  // namespace google
}  // namespace protobuf
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_robotcommon_2eproto__INCLUDED
