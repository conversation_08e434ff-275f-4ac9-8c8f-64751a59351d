#ifndef ROBOTIOMATETYPE_H
#define ROBOTIOMATETYPE_H

//接口板用户DI 名称定义
#define UESR_IO_DI_00_NAME  "U_DI_00"
#define UESR_IO_DI_01_NAME  "U_DI_01"
#define UESR_IO_DI_02_NAME  "U_DI_02"
#define UESR_IO_DI_03_NAME  "U_DI_03"
#define UESR_IO_DI_04_NAME  "U_DI_04"
#define UESR_IO_DI_05_NAME  "U_DI_05"
#define UESR_IO_DI_06_NAME  "U_DI_06"
#define UESR_IO_DI_07_NAME  "U_DI_07"
#define UESR_IO_DI_10_NAME  "U_DI_10"
#define UESR_IO_DI_11_NAME  "U_DI_11"
#define UESR_IO_DI_12_NAME  "U_DI_12"
#define UESR_IO_DI_13_NAME  "U_DI_13"
#define UESR_IO_DI_14_NAME  "U_DI_14"
#define UESR_IO_DI_15_NAME  "U_DI_15"
#define UESR_IO_DI_16_NAME  "U_DI_16"
#define UESR_IO_DI_17_NAME  "U_DI_17"

//接口板用户DI  地址定义
#define UESR_IO_DI_00_ADDR  0X24
#define UESR_IO_DI_01_ADDR  0X25
#define UESR_IO_DI_02_ADDR  0X26
#define UESR_IO_DI_03_ADDR  0X27
#define UESR_IO_DI_04_ADDR  0X28
#define UESR_IO_DI_05_ADDR  0X29
#define UESR_IO_DI_06_ADDR  0X2A
#define UESR_IO_DI_07_ADDR  0X2B
#define UESR_IO_DI_10_ADDR  0X2C
#define UESR_IO_DI_11_ADDR  0X2D
#define UESR_IO_DI_12_ADDR  0X2E
#define UESR_IO_DI_13_ADDR  0X2F
#define UESR_IO_DI_14_ADDR  0X30
#define UESR_IO_DI_15_ADDR  0X31
#define UESR_IO_DI_16_ADDR  0X32
#define UESR_IO_DI_17_ADDR  0X33


//接口板用户DO 名称定义
#define UESR_IO_DO_00_NAME  "U_DO_00"
#define UESR_IO_DO_01_NAME  "U_DO_01"
#define UESR_IO_DO_02_NAME  "U_DO_02"
#define UESR_IO_DO_03_NAME  "U_DO_03"
#define UESR_IO_DO_04_NAME  "U_DO_04"
#define UESR_IO_DO_05_NAME  "U_DO_05"
#define UESR_IO_DO_06_NAME  "U_DO_06"
#define UESR_IO_DO_07_NAME  "U_DO_07"
#define UESR_IO_DO_10_NAME  "U_DO_10"
#define UESR_IO_DO_11_NAME  "U_DO_11"
#define UESR_IO_DO_12_NAME  "U_DO_12"
#define UESR_IO_DO_13_NAME  "U_DO_13"
#define UESR_IO_DO_14_NAME  "U_DO_14"
#define UESR_IO_DO_15_NAME  "U_DO_15"
#define UESR_IO_DO_16_NAME  "U_DO_16"
#define UESR_IO_DO_17_NAME  "U_DO_17"

//接口板用户DO  地址定义
#define UESR_IO_DO_00_ADDR  0X20
#define UESR_IO_DO_01_ADDR  0X21
#define UESR_IO_DO_02_ADDR  0X22
#define UESR_IO_DO_03_ADDR  0X23
#define UESR_IO_DO_04_ADDR  0X24
#define UESR_IO_DO_05_ADDR  0X25
#define UESR_IO_DO_06_ADDR  0X26
#define UESR_IO_DO_07_ADDR  0X27
#define UESR_IO_DO_10_ADDR  0X28
#define UESR_IO_DO_11_ADDR  0X29
#define UESR_IO_DO_12_ADDR  0X2A
#define UESR_IO_DO_13_ADDR  0X2B
#define UESR_IO_DO_14_ADDR  0X2C
#define UESR_IO_DO_15_ADDR  0X2D
#define UESR_IO_DO_16_ADDR  0X2E
#define UESR_IO_DO_17_ADDR  0X2F


//接口板用户AI  名称定义
#define UESR_IO_AI_00_NAME  "VI0"
#define UESR_IO_AI_01_NAME  "VI1"
#define UESR_IO_AI_02_NAME  "VI2"
#define UESR_IO_AI_03_NAME  "VI3"

//接口板用户AI  地址定义
#define UESR_IO_AI_00_ADDR  0X00
#define UESR_IO_AI_01_ADDR  0X01
#define UESR_IO_AI_02_ADDR  0X02
#define UESR_IO_AI_03_ADDR  0X03



//接口板用户AI  名称定义
#define UESR_IO_AO_00_NAME  "VO0"
#define UESR_IO_AO_01_NAME  "VO1"
#define UESR_IO_AO_02_NAME  "VO2"
#define UESR_IO_AO_03_NAME  "VO3"

//接口板用户AI  地址定义
#define UESR_IO_AO_00_ADDR  0X00
#define UESR_IO_AO_01_ADDR  0X01
#define UESR_IO_AO_02_ADDR  0X02
#define UESR_IO_AO_03_ADDR  0X03


#endif // ROBOTIOMATETYPE_H
